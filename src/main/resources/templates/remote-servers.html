<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>远程服务器管理 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1e293b',
                        accent: '#2563eb',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-slate-50 via-gray-50 to-slate-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white/95 backdrop-blur-sm shadow-lg border-b border-white/20 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="/" class="flex items-center space-x-3 text-gray-900 hover:text-gray-600 transition-colors">
                        <div class="w-8 h-8 bg-gradient-to-r from-gray-600 to-slate-600 rounded-lg flex items-center justify-center">
                            <i class="bi bi-arrow-left text-white text-sm"></i>
                        </div>
                        <span class="font-semibold">返回主页</span>
                    </a>
                </div>
                <div class="flex items-center">
                    <div class="text-center">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-gray-600 to-slate-600 bg-clip-text text-transparent">远程服务器管理</h1>
                        <p class="text-xs text-gray-500">Remote Server Management</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">SSH连接</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                <i class="bi bi-server text-gray-600 mr-3"></i>
                SSH远程服务器管理平台
            </h2>
            <p class="text-gray-600">统一管理远程服务器，支持SSH连接、用户管理、软件安装、文件传输等功能</p>
        </div>

        <!-- Tabs -->
        <div class="mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg border border-white/20">
                <nav class="flex space-x-2" aria-label="Tabs">
                    <button id="tab-servers" class="tab-button active" onclick="showTab('servers')">
                        <i class="bi bi-server mr-2"></i>服务器列表
                    </button>
                    <button id="tab-ssh-connections" class="tab-button" onclick="showTab('ssh-connections')">
                        <i class="bi bi-terminal mr-2"></i>SSH连接
                    </button>
                    <button id="tab-operations" class="tab-button" onclick="showTab('operations')">
                        <i class="bi bi-list-task mr-2"></i>操作记录
                    </button>
                    <button id="tab-statistics" class="tab-button" onclick="showTab('statistics')">
                        <i class="bi bi-graph-up mr-2"></i>统计信息
                    </button>
                </nav>
            </div>
        </div>

        <!-- Servers Tab -->
        <div id="servers-tab" class="tab-content">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-medium text-gray-900">服务器列表</h2>
                    <div class="flex space-x-2">
                        <button onclick="showAddServerModal()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            <i class="bi bi-plus mr-2"></i>添加服务器
                        </button>
                        <button onclick="refreshServers()" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                            <i class="bi bi-arrow-clockwise mr-2"></i>刷新
                        </button>
                    </div>
                </div>
                
                <div id="serversGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Server cards will be loaded here -->
                </div>
            </div>
        </div>

        <!-- SSH Connections Tab -->
        <div id="ssh-connections-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-medium text-gray-900">SSH连接管理</h2>
                    <button onclick="showAddSshConnectionModal()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                        <i class="bi bi-plus mr-2"></i>添加连接
                    </button>
                </div>
                
                <div id="sshConnectionsTable" class="overflow-x-auto">
                    <!-- SSH connections table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Operations Tab -->
        <div id="operations-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">操作记录</h2>
                
                <div id="operationsTable" class="overflow-x-auto">
                    <!-- Operations table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Statistics Tab -->
        <div id="statistics-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">统计信息</h2>
                
                <div id="statisticsContent">
                    <!-- Statistics content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Server Management Modal -->
    <div id="serverManagementModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="px-6 py-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900" id="serverModalTitle">服务器管理</h3>
                    <button onclick="closeServerManagementModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                
                <div class="p-6">
                    <!-- Server management tabs -->
                    <div class="mb-4">
                        <nav class="flex space-x-4" id="serverManagementTabs">
                            <button class="server-tab-button active" data-tab="info" onclick="showServerTab('info')">
                                <i class="bi bi-info-circle mr-2"></i>基本信息
                            </button>
                            <button class="server-tab-button" data-tab="users" onclick="showServerTab('users')">
                                <i class="bi bi-people mr-2"></i>用户管理
                            </button>
                            <button class="server-tab-button" data-tab="software" onclick="showServerTab('software')">
                                <i class="bi bi-box mr-2"></i>软件管理
                            </button>
                            <button class="server-tab-button" data-tab="nginx" onclick="showServerTab('nginx')">
                                <i class="bi bi-globe mr-2"></i>Nginx
                            </button>
                            <button class="server-tab-button" data-tab="caddy" onclick="showServerTab('caddy')">
                                <i class="bi bi-lightning mr-2"></i>Caddy
                            </button>
                            <button class="server-tab-button" data-tab="files" onclick="showServerTab('files')">
                                <i class="bi bi-folder mr-2"></i>文件传输
                            </button>
                        </nav>
                    </div>
                    
                    <!-- Tab contents -->
                    <div id="serverTabContent">
                        <!-- Content will be loaded dynamically -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Server Modal -->
    <div id="addServerModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900">添加服务器</h3>
                    <button onclick="closeAddServerModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                
                <form id="addServerForm" class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">服务器名称</label>
                            <input type="text" id="serverName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">SSH连接</label>
                            <select id="sshConnectionId" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                                <option value="">选择SSH连接</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">环境</label>
                            <select id="environment" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                                <option value="DEVELOPMENT">开发环境</option>
                                <option value="TESTING">测试环境</option>
                                <option value="STAGING">预发布环境</option>
                                <option value="PRODUCTION">生产环境</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">公网IP</label>
                            <input type="text" id="publicIp" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">内网IP</label>
                            <input type="text" id="privateIp" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">地区</label>
                            <input type="text" id="region" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">提供商</label>
                            <input type="text" id="provider" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700">描述</label>
                        <textarea id="description" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddServerModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            取消
                        </button>
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            添加
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add SSH Connection Modal -->
    <div id="addSshConnectionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900">添加SSH连接</h3>
                    <button onclick="closeAddSshConnectionModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                
                <form id="addSshConnectionForm" class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">连接名称</label>
                            <input type="text" id="connectionName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">主机地址</label>
                            <input type="text" id="host" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">端口</label>
                            <input type="number" id="port" value="22" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">用户名</label>
                            <input type="text" id="username" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">认证类型</label>
                            <select id="authType" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" onchange="toggleAuthFields()">
                                <option value="PASSWORD">密码认证</option>
                                <option value="PRIVATE_KEY">私钥认证</option>
                                <option value="PRIVATE_KEY_WITH_PASSWORD">私钥+密码认证</option>
                            </select>
                        </div>
                        <div id="passwordField">
                            <label class="block text-sm font-medium text-gray-700">密码</label>
                            <input type="password" id="password" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div id="privateKeyField" class="hidden col-span-2">
                            <label class="block text-sm font-medium text-gray-700">私钥内容</label>
                            <textarea id="privateKey" rows="4" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700">描述</label>
                        <textarea id="sshDescription" rows="2" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddSshConnectionModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            取消
                        </button>
                        <button type="button" onclick="testSshConnection()" class="bg-warning text-white px-4 py-2 rounded-md hover:bg-yellow-600">
                            测试连接
                        </button>
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            添加
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script src="/js/file-transfer.js"></script>
    <script>
        let currentServerId = null;

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            // Load tab content
            if (tabName === 'servers') {
                loadServers();
            } else if (tabName === 'ssh-connections') {
                loadSshConnections();
            } else if (tabName === 'operations') {
                loadOperations();
            } else if (tabName === 'statistics') {
                loadStatistics();
            }
        }

        // Load servers
        async function loadServers() {
            try {
                const response = await fetch('/api/remote-servers');
                const servers = await response.json();
                
                const serversGrid = document.getElementById('serversGrid');
                serversGrid.innerHTML = servers.map(server => `
                    <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-semibold text-lg">${server.serverName}</h3>
                            <span class="px-2 py-1 text-xs rounded-full ${getStatusColor(server.status)}">
                                ${getStatusText(server.status)}
                            </span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div><i class="bi bi-geo-alt mr-1"></i>${server.publicIp || server.privateIp || 'N/A'}</div>
                            <div><i class="bi bi-hdd mr-1"></i>${server.osType || 'Unknown'}</div>
                            <div><i class="bi bi-building mr-1"></i>${server.environment}</div>
                        </div>
                        <div class="mt-3 flex space-x-2">
                            <button onclick="openServerManagement(${server.id})" class="bg-primary text-white px-3 py-1 text-sm rounded hover:bg-accent">
                                管理
                            </button>
                            <button onclick="checkServerStatus(${server.id})" class="bg-gray-500 text-white px-3 py-1 text-sm rounded hover:bg-gray-600">
                                检查状态
                            </button>
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                showToast('加载服务器列表失败: ' + error.message, 'error');
            }
        }

        // Load SSH connections
        async function loadSshConnections() {
            try {
                const response = await fetch('/api/remote-servers/ssh-connections');
                const connections = await response.json();
                
                const table = document.getElementById('sshConnectionsTable');
                table.innerHTML = `
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">连接名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主机</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户名</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">认证类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${connections.map(conn => `
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${conn.connectionName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${conn.host}:${conn.port}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${conn.username}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${conn.authType}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="testConnection(${conn.id})" class="text-primary hover:text-accent mr-3">测试</button>
                                        <button onclick="deleteConnection(${conn.id})" class="text-danger hover:text-red-700">删除</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            } catch (error) {
                showToast('加载SSH连接失败: ' + error.message, 'error');
            }
        }

        // Helper functions
        function getStatusColor(status) {
            switch(status) {
                case 'ONLINE': return 'bg-green-100 text-green-800';
                case 'OFFLINE': return 'bg-red-100 text-red-800';
                case 'MAINTENANCE': return 'bg-yellow-100 text-yellow-800';
                case 'ERROR': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getStatusText(status) {
            switch(status) {
                case 'ONLINE': return '在线';
                case 'OFFLINE': return '离线';
                case 'MAINTENANCE': return '维护中';
                case 'ERROR': return '错误';
                default: return '未知';
            }
        }

        // Server tab switching
        function showServerTab(tabName) {
            // Remove active class from all server tab buttons
            document.querySelectorAll('.server-tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Add active class to selected tab button
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // Load tab content based on tab name
            if (tabName === 'files') {
                fileTransferManager.showFileTransferInterface(currentServerId);
            } else {
                // Handle other tabs (info, users, software, nginx, caddy)
                loadServerTabContent(tabName);
            }
        }

        // Load server tab content (placeholder for other tabs)
        function loadServerTabContent(tabName) {
            const content = `
                <div class="text-center py-8">
                    <i class="bi bi-gear text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">${getTabTitle(tabName)}</h3>
                    <p class="text-gray-500">功能开发中...</p>
                </div>
            `;
            document.getElementById('serverTabContent').innerHTML = content;
        }

        // Get tab title
        function getTabTitle(tabName) {
            const titles = {
                'info': '服务器信息',
                'users': '用户管理',
                'software': '软件管理',
                'nginx': 'Nginx管理',
                'caddy': 'Caddy管理'
            };
            return titles[tabName] || '未知';
        }

        // Open server management modal
        function openServerManagement(serverId) {
            currentServerId = serverId;
            document.getElementById('serverManagementModal').classList.remove('hidden');
            showServerTab('info'); // Default to info tab
        }

        // Close server management modal
        function closeServerManagementModal() {
            document.getElementById('serverManagementModal').classList.add('hidden');
            currentServerId = null;
        }

        // Show add server modal
        function showAddServerModal() {
            document.getElementById('addServerModal').classList.remove('hidden');
            loadSshConnectionsForSelect();
        }

        // Close add server modal
        function closeAddServerModal() {
            document.getElementById('addServerModal').classList.add('hidden');
        }

        // Show add SSH connection modal
        function showAddSshConnectionModal() {
            document.getElementById('addSshConnectionModal').classList.remove('hidden');
        }

        // Close add SSH connection modal
        function closeAddSshConnectionModal() {
            document.getElementById('addSshConnectionModal').classList.add('hidden');
        }

        // Toggle auth fields based on auth type
        function toggleAuthFields() {
            const authType = document.getElementById('authType').value;
            const passwordField = document.getElementById('passwordField');
            const privateKeyField = document.getElementById('privateKeyField');

            if (authType === 'PASSWORD') {
                passwordField.classList.remove('hidden');
                privateKeyField.classList.add('hidden');
            } else if (authType === 'PRIVATE_KEY') {
                passwordField.classList.add('hidden');
                privateKeyField.classList.remove('hidden');
            } else if (authType === 'PRIVATE_KEY_WITH_PASSWORD') {
                passwordField.classList.remove('hidden');
                privateKeyField.classList.remove('hidden');
            }
        }

        // Load SSH connections for server form
        async function loadSshConnectionsForSelect() {
            try {
                const response = await fetch('/api/remote-servers/ssh-connections');
                const connections = await response.json();

                const select = document.getElementById('sshConnectionId');
                select.innerHTML = '<option value="">选择SSH连接</option>';

                connections.forEach(conn => {
                    const option = document.createElement('option');
                    option.value = conn.id;
                    option.textContent = conn.displayName || `${conn.connectionName} (${conn.host})`;
                    select.appendChild(option);
                });
            } catch (error) {
                showToast('加载SSH连接失败: ' + error.message, 'error');
            }
        }

        // Check server status
        async function checkServerStatus(serverId) {
            try {
                const response = await fetch(`/api/remote-servers/${serverId}/check-status`, {
                    method: 'POST'
                });

                if (response.ok) {
                    showToast('状态检查已开始', 'success');
                    setTimeout(() => loadServers(), 2000); // Refresh after 2 seconds
                } else {
                    showToast('状态检查失败', 'error');
                }
            } catch (error) {
                showToast('状态检查错误: ' + error.message, 'error');
            }
        }

        // Refresh servers
        function refreshServers() {
            loadServers();
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            showTab('servers');
        });
    </script>

    <style>
        .tab-button {
            @apply py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300;
        }
        
        .tab-button.active {
            @apply border-primary text-primary;
        }

        .server-tab-button {
            @apply py-2 px-4 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300;
        }
        
        .server-tab-button.active {
            @apply border-primary text-primary;
        }
    </style>
</body>
</html>
