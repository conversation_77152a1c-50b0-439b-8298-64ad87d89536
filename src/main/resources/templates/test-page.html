<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white/95 backdrop-blur-sm shadow-lg border-b border-white/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="/" class="flex items-center space-x-3 text-gray-900 hover:text-blue-600 transition-colors">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="bi bi-arrow-left text-white text-sm"></i>
                        </div>
                        <span class="font-semibold">返回主页</span>
                    </a>
                </div>
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">测试页面</h1>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="w-24 h-24 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="bi bi-check-circle text-white text-4xl"></i>
            </div>
            
            <h1 class="text-4xl font-bold text-gray-900 mb-4">导航成功！</h1>
            <p class="text-xl text-gray-600 mb-8">
                恭喜！您已经成功从首页导航到了这个测试页面。
            </p>
            
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-8 border border-white/20 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">功能测试结果</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="bi bi-check-lg text-green-600 text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900">点击功能</h3>
                        <p class="text-gray-600">模块卡片点击正常工作</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="bi bi-arrow-right-circle text-blue-600 text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900">页面跳转</h3>
                        <p class="text-gray-600">路由导航功能正常</p>
                    </div>
                </div>
            </div>
            
            <div class="space-y-4">
                <a href="/" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105">
                    <i class="bi bi-house mr-2"></i>
                    返回首页
                </a>
                
                <div class="flex flex-wrap justify-center gap-4 mt-6">
                    <a href="/database-management" class="px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors">
                        <i class="bi bi-database mr-2"></i>数据库管理
                    </a>
                    <a href="/project-management" class="px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors">
                        <i class="bi bi-folder mr-2"></i>项目管理
                    </a>
                    <a href="/monitoring" class="px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors">
                        <i class="bi bi-activity mr-2"></i>系统监控
                    </a>
                    <a href="/log-management" class="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">
                        <i class="bi bi-file-text mr-2"></i>日志管理
                    </a>
                    <a href="/api-management" class="px-4 py-2 bg-teal-100 text-teal-700 rounded-lg hover:bg-teal-200 transition-colors">
                        <i class="bi bi-cloud mr-2"></i>API管理
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
