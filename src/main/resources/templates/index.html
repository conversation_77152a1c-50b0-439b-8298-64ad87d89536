<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spring Boot Application Manager</title>

    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="/css/loading.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1e293b',
                        accent: '#2563eb',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        purple: '#8b5cf6',
                        teal: '#14b8a6',
                        orange: '#f97316'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'bounce-in': 'bounceIn 1s ease-out',
                        'float': 'float 3s ease-in-out infinite'
                    },
                    backdropBlur: {
                        xs: '2px'
                    }
                }
            }
        }
    </script>

    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }

        .card-hover {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .glass-effect {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-glass {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.95);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .module-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(226, 232, 240, 0.8);
            transition: all 0.3s ease;
        }

        .module-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .icon-gradient {
            background: linear-gradient(135deg, var(--tw-gradient-from), var(--tw-gradient-to));
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 min-h-screen font-sans">
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="spinner"></div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-[9999]"></div>

    <!-- Hero Section -->
    <div class="hero-gradient min-h-screen relative overflow-hidden">
        <!-- Floating Elements -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full animate-float"></div>
            <div class="absolute top-40 -left-20 w-60 h-60 bg-white opacity-5 rounded-full animate-float" style="animation-delay: 1s;"></div>
            <div class="absolute bottom-20 right-20 w-40 h-40 bg-white opacity-10 rounded-full animate-float" style="animation-delay: 2s;"></div>
        </div>

        <!-- Navigation -->
        <nav class="nav-glass sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                            <i class="bi bi-gear-fill text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900">Spring Boot Manager</h1>
                            <p class="text-xs text-gray-600">企业级应用管理平台</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="hidden md:flex items-center space-x-3">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <span class="text-sm text-gray-700">系统在线</span>
                            </div>
                            <div class="text-sm text-gray-500">v1.0.0</div>
                        </div>
                        <button class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="bi bi-bell text-gray-600 hover:text-gray-900"></i>
                        </button>
                        <button class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="bi bi-person-circle text-gray-600 hover:text-gray-900 text-xl"></i>
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Hero Content -->
        <div class="relative z-10 flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
            <div class="text-center max-w-4xl mx-auto">
                <!-- Main Title -->
                <div class="animate-fade-in">
                    <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
                        Spring Boot
                        <span class="block bg-gradient-to-r from-yellow-200 to-pink-200 bg-clip-text text-transparent">
                            Application Manager
                        </span>
                    </h1>
                    <p class="text-xl md:text-2xl text-white/90 mb-12 leading-relaxed">
                        企业级应用管理平台，提供完整的开发、部署、监控解决方案
                    </p>
                </div>

                <!-- Feature Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16 animate-slide-up">
                    <div class="glass-effect rounded-2xl p-6 text-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <i class="bi bi-box text-white text-xl"></i>
                        </div>
                        <div class="text-2xl font-bold text-white mb-1" id="totalApps">9</div>
                        <div class="text-sm text-white/70">功能模块</div>
                    </div>
                    <div class="glass-effect rounded-2xl p-6 text-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <i class="bi bi-play-circle text-white text-xl"></i>
                        </div>
                        <div class="text-2xl font-bold text-white mb-1" id="runningApps">100+</div>
                        <div class="text-sm text-white/70">API接口</div>
                    </div>
                    <div class="glass-effect rounded-2xl p-6 text-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <i class="bi bi-shield-check text-white text-xl"></i>
                        </div>
                        <div class="text-2xl font-bold text-white mb-1" id="secureApps">企业级</div>
                        <div class="text-sm text-white/70">安全保障</div>
                    </div>
                    <div class="glass-effect rounded-2xl p-6 text-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-orange-400 to-orange-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <i class="bi bi-lightning text-white text-xl"></i>
                        </div>
                        <div class="text-2xl font-bold text-white mb-1" id="fastApps">高性能</div>
                        <div class="text-sm text-white/70">响应速度</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="animate-bounce-in">
                    <h2 class="text-2xl font-bold text-white text-center mb-8">快速开始</h2>
                    <div class="flex flex-wrap justify-center gap-4">
                        <button onclick="navigateToModule('/code-generator')" class="glass-effect px-8 py-4 rounded-2xl hover:bg-white/20 transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <i class="bi bi-code-slash text-white text-xl mr-3"></i>
                            <span class="text-white font-medium">代码生成</span>
                        </button>
                        <button onclick="navigateToModule('/database-management')" class="glass-effect px-8 py-4 rounded-2xl hover:bg-white/20 transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <i class="bi bi-database text-white text-xl mr-3"></i>
                            <span class="text-white font-medium">数据库管理</span>
                        </button>
                        <button onclick="navigateToModule('/monitoring')" class="glass-effect px-8 py-4 rounded-2xl hover:bg-white/20 transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <i class="bi bi-activity text-white text-xl mr-3"></i>
                            <span class="text-white font-medium">系统监控</span>
                        </button>
                        <button onclick="navigateToModule('/ai-assistant')" class="glass-effect px-8 py-4 rounded-2xl hover:bg-white/20 transition-all duration-300 transform hover:scale-105 cursor-pointer">
                            <i class="bi bi-robot text-white text-xl mr-3"></i>
                            <span class="text-white font-medium">AI助手</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="bg-white py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold text-gray-900 mb-4">功能模块</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        完整的企业级应用管理解决方案，涵盖开发、部署、监控、运维的全生命周期
                    </p>
                </div>

                <!-- Module Cards Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Code Generator -->
                    <div class="module-card rounded-2xl p-8 card-hover group cursor-pointer" onclick="navigateToModule('/code-generator')">
                        <div class="w-16 h-16 icon-gradient from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="bi bi-code-slash text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">代码生成器</h3>
                        <p class="text-gray-600 mb-4">智能代码生成，支持Spring Boot项目脚手架，提高开发效率</p>
                        <div class="flex items-center text-blue-600 font-medium">
                            <span>立即使用</span>
                            <i class="bi bi-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                        </div>
                    </div>

                    <!-- Database Management -->
                    <div class="module-card rounded-2xl p-8 card-hover group cursor-pointer" onclick="navigateToModule('/database-management')">
                        <div class="w-16 h-16 icon-gradient from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="bi bi-database text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">数据库管理</h3>
                        <p class="text-gray-600 mb-4">多数据库连接管理，SQL执行器，数据导入导出功能</p>
                        <div class="flex items-center text-green-600 font-medium">
                            <span>立即使用</span>
                            <i class="bi bi-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                        </div>
                    </div>

                    <!-- Project Management -->
                    <div class="module-card rounded-2xl p-8 card-hover group cursor-pointer" onclick="navigateToModule('/project-management')">
                        <div class="w-16 h-16 icon-gradient from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="bi bi-folder text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">项目管理</h3>
                        <p class="text-gray-600 mb-4">项目生命周期管理，成员权限控制，环境配置管理</p>
                        <div class="flex items-center text-purple-600 font-medium">
                            <span>立即使用</span>
                            <i class="bi bi-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                        </div>
                    </div>

                    <!-- Config Management -->
                    <div class="module-card rounded-2xl p-8 card-hover group cursor-pointer" onclick="navigateToModule('/config-management')">
                        <div class="w-16 h-16 icon-gradient from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="bi bi-gear text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">配置管理</h3>
                        <p class="text-gray-600 mb-4">多环境配置管理，配置加密，版本控制和热更新</p>
                        <div class="flex items-center text-orange-600 font-medium">
                            <span>立即使用</span>
                            <i class="bi bi-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                        </div>
                    </div>

                    <!-- User Management -->
                    <div class="module-card rounded-2xl p-8 card-hover group cursor-pointer" onclick="navigateToModule('/user-management')">
                        <div class="w-16 h-16 icon-gradient from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="bi bi-people text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">用户管理</h3>
                        <p class="text-gray-600 mb-4">用户认证授权，角色权限管理，安全审计功能</p>
                        <div class="flex items-center text-indigo-600 font-medium">
                            <span>立即使用</span>
                            <i class="bi bi-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                        </div>
                    </div>

                    <!-- System Monitoring -->
                    <div class="module-card rounded-2xl p-8 card-hover group cursor-pointer" onclick="navigateToModule('/monitoring')">
                        <div class="w-16 h-16 icon-gradient from-red-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="bi bi-activity text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">系统监控</h3>
                        <p class="text-gray-600 mb-4">实时系统监控，智能告警，性能分析和容量规划</p>
                        <div class="flex items-center text-red-600 font-medium">
                            <span>立即使用</span>
                            <i class="bi bi-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                        </div>
                    </div>

                    <!-- Log Management -->
                    <div class="module-card rounded-2xl p-8 card-hover group cursor-pointer" onclick="navigateToModule('/log-management')">
                        <div class="w-16 h-16 icon-gradient from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="bi bi-file-text text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">日志管理</h3>
                        <p class="text-gray-600 mb-4">集中式日志管理，智能分析，操作审计和错误追踪</p>
                        <div class="flex items-center text-purple-600 font-medium">
                            <span>立即使用</span>
                            <i class="bi bi-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                        </div>
                    </div>

                    <!-- API Management -->
                    <div class="module-card rounded-2xl p-8 card-hover group cursor-pointer" onclick="navigateToModule('/api-management')">
                        <div class="w-16 h-16 icon-gradient from-teal-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="bi bi-cloud text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">API管理</h3>
                        <p class="text-gray-600 mb-4">API文档管理，在线测试，性能监控和版本控制</p>
                        <div class="flex items-center text-teal-600 font-medium">
                            <span>立即使用</span>
                            <i class="bi bi-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                        </div>
                    </div>

                    <!-- AI Assistant -->
                    <div class="module-card rounded-2xl p-8 card-hover group cursor-pointer" onclick="navigateToModule('/ai-assistant')">
                        <div class="w-16 h-16 icon-gradient from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="bi bi-robot text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-3">AI助手</h3>
                        <p class="text-gray-600 mb-4">智能代码生成，问题诊断，性能优化建议</p>
                        <div class="flex items-center text-violet-600 font-medium">
                            <span>立即使用</span>
                            <i class="bi bi-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="bg-gray-900 text-white py-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div>
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                                <i class="bi bi-gear-fill text-white"></i>
                            </div>
                            <h3 class="text-xl font-bold">Spring Boot Manager</h3>
                        </div>
                        <p class="text-gray-400">企业级应用管理平台，提供完整的开发、部署、监控解决方案。</p>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold mb-4">核心功能</h4>
                        <ul class="space-y-2 text-gray-400">
                            <li>• 代码生成与项目管理</li>
                            <li>• 数据库管理与配置</li>
                            <li>• 系统监控与告警</li>
                            <li>• 日志分析与API管理</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold mb-4">技术支持</h4>
                        <ul class="space-y-2 text-gray-400">
                            <li>• Spring Boot 2.x</li>
                            <li>• 微服务架构</li>
                            <li>• 企业级安全</li>
                            <li>• 高可用部署</li>
                        </ul>
                    </div>
                </div>
                <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                    <p>&copy; 2024 Spring Boot Application Manager. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 隐藏加载遮罩
            hideLoadingOverlay();

            // 添加页面加载完成的动画效果
            const elements = document.querySelectorAll('.animate-fade-in, .animate-slide-up, .animate-bounce-in');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
            });

            // 模拟加载统计数据
            setTimeout(() => {
                updateStats();
            }, 1000);
        });

        // 更新统计数据
        function updateStats() {
            const stats = [
                { id: 'totalApps', value: '9', suffix: '' },
                { id: 'runningApps', value: '100+', suffix: '' },
                { id: 'secureApps', value: '企业级', suffix: '' },
                { id: 'fastApps', value: '高性能', suffix: '' }
            ];

            stats.forEach(stat => {
                const element = document.getElementById(stat.id);
                if (element) {
                    animateValue(element, stat.value);
                }
            });
        }

        // 数字动画效果
        function animateValue(element, targetValue) {
            if (typeof targetValue === 'string' && !targetValue.match(/^\d+/)) {
                element.textContent = targetValue;
                return;
            }

            const numericValue = parseInt(targetValue.replace(/\D/g, ''));
            const suffix = targetValue.replace(/\d/g, '');
            let currentValue = 0;
            const increment = numericValue / 30;
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= numericValue) {
                    element.textContent = targetValue;
                    clearInterval(timer);
                } else {
                    element.textContent = Math.floor(currentValue) + suffix;
                }
            }, 50);
        }

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 添加滚动效果
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('nav');
            if (window.scrollY > 100) {
                nav.classList.add('backdrop-blur-md');
            } else {
                nav.classList.remove('backdrop-blur-md');
            }
        });

        // 卡片悬停效果增强
        document.querySelectorAll('.module-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // 导航到模块
        function navigateToModule(url) {
            // 添加加载效果
            showLoadingOverlay();

            // 显示导航提示
            showToast('正在跳转到模块...', 'info');

            // 延迟跳转以显示加载效果
            setTimeout(() => {
                window.location.href = url;
            }, 500);
        }

        // 显示加载遮罩
        function showLoadingOverlay() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.classList.remove('hidden');
            }
        }

        // 隐藏加载遮罩
        function hideLoadingOverlay() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.classList.add('hidden');
            }
        }

        // 显示Toast消息
        function showToast(message, type = 'info') {
            if (typeof window.showToast === 'function') {
                window.showToast(message, type);
            } else {
                // 简单的Toast实现
                const toast = document.createElement('div');
                toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white font-medium transform transition-all duration-300 translate-x-full`;

                // 根据类型设置颜色
                switch(type) {
                    case 'success':
                        toast.classList.add('bg-green-500');
                        break;
                    case 'error':
                        toast.classList.add('bg-red-500');
                        break;
                    case 'warning':
                        toast.classList.add('bg-yellow-500');
                        break;
                    default:
                        toast.classList.add('bg-blue-500');
                }

                toast.textContent = message;
                document.body.appendChild(toast);

                // 显示动画
                setTimeout(() => {
                    toast.classList.remove('translate-x-full');
                }, 100);

                // 自动隐藏
                setTimeout(() => {
                    toast.classList.add('translate-x-full');
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 300);
                }, 3000);
            }
        }

        // 页面性能监控
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            console.log(`页面加载时间: ${Math.round(loadTime)}ms`);

            if (loadTime > 3000) {
                console.warn('页面加载时间较长，建议优化');
            }
        });
    </script>
</body>
</html> 
