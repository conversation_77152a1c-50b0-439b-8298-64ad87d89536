<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能助手 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1e293b',
                        accent: '#2563eb',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-slate-50 via-violet-50 to-purple-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white/95 backdrop-blur-sm shadow-lg border-b border-white/20 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="/" class="flex items-center space-x-3 text-gray-900 hover:text-violet-600 transition-colors">
                        <div class="w-8 h-8 bg-gradient-to-r from-violet-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="bi bi-arrow-left text-white text-sm"></i>
                        </div>
                        <span class="font-semibold">返回主页</span>
                    </a>
                </div>
                <div class="flex items-center">
                    <div class="text-center">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">AI智能助手</h1>
                        <p class="text-xs text-gray-500">AI Assistant</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">AI就绪</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                <i class="bi bi-robot text-violet-600 mr-3"></i>
                AI智能助手平台
            </h2>
            <p class="text-gray-600">集成多种AI模型，提供智能对话、代码生成、问题诊断等功能</p>
        </div>

        <!-- Tabs -->
        <div class="mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg border border-white/20">
                <nav class="flex space-x-2" aria-label="Tabs">
                    <button id="tab-chat" class="tab-button active" onclick="showTab('chat')">
                        <i class="bi bi-chat-dots mr-2"></i>智能对话
                    </button>
                    <button id="tab-code" class="tab-button" onclick="showTab('code')">
                        <i class="bi bi-code-slash mr-2"></i>代码助手
                    </button>
                    <button id="tab-providers" class="tab-button" onclick="showTab('providers')">
                        <i class="bi bi-gear mr-2"></i>AI配置
                    </button>
                    <button id="tab-history" class="tab-button" onclick="showTab('history')">
                        <i class="bi bi-clock-history mr-2"></i>对话历史
                    </button>
                </nav>
            </div>
        </div>

        <!-- Chat Tab -->
        <div id="chat-tab" class="tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Conversation List -->
                <div class="lg:col-span-1">
                    <div class="bg-white shadow rounded-lg p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">对话列表</h3>
                            <button onclick="createNewConversation()" class="bg-primary text-white px-3 py-1 rounded text-sm hover:bg-accent">
                                <i class="bi bi-plus"></i> 新对话
                            </button>
                        </div>
                        <div id="conversationList" class="space-y-2">
                            <!-- Conversations will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Chat Area -->
                <div class="lg:col-span-3">
                    <div class="bg-white shadow rounded-lg h-96 flex flex-col">
                        <!-- Chat Header -->
                        <div class="border-b p-4">
                            <div class="flex justify-between items-center">
                                <h3 id="chatTitle" class="text-lg font-medium text-gray-900">选择或创建对话</h3>
                                <div class="flex space-x-2">
                                    <select id="aiProviderSelect" class="text-sm border-gray-300 rounded-md">
                                        <option value="">选择AI提供商</option>
                                    </select>
                                    <button onclick="clearChat()" class="text-gray-500 hover:text-gray-700">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Messages Area -->
                        <div id="messagesArea" class="flex-1 overflow-y-auto p-4 space-y-4">
                            <div class="text-center text-gray-500 py-8">
                                <i class="bi bi-chat-dots text-4xl mb-4"></i>
                                <p>开始与AI助手对话</p>
                            </div>
                        </div>

                        <!-- Input Area -->
                        <div class="border-t p-4">
                            <div class="flex space-x-2">
                                <input type="text" id="messageInput" placeholder="输入您的问题..."
                                       class="flex-1 border-2 border-gray-400 rounded-lg focus:ring-2 focus:ring-violet-200 focus:border-violet-500 px-4 py-3 hover:border-gray-500 transition-all duration-300"
                                       onkeypress="handleKeyPress(event)">
                                <button onclick="sendMessage()" id="sendButton" 
                                        class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent disabled:opacity-50">
                                    <i class="bi bi-send"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Code Assistant Tab -->
        <div id="code-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Code Input -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">代码助手</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">功能类型</label>
                            <select id="codeFunction" class="w-full border-2 border-gray-400 rounded-lg px-4 py-3 focus:ring-2 focus:ring-violet-200 focus:border-violet-500 hover:border-gray-500 transition-all duration-300">
                                <option value="generate">代码生成</option>
                                <option value="review">代码审查</option>
                                <option value="explain">代码解释</option>
                                <option value="document">生成文档</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">编程语言</label>
                            <select id="codeLanguage" class="w-full border-2 border-gray-400 rounded-lg px-4 py-3 focus:ring-2 focus:ring-violet-200 focus:border-violet-500 hover:border-gray-500 transition-all duration-300">
                                <option value="java">Java</option>
                                <option value="javascript">JavaScript</option>
                                <option value="python">Python</option>
                                <option value="typescript">TypeScript</option>
                                <option value="go">Go</option>
                                <option value="rust">Rust</option>
                                <option value="cpp">C++</option>
                            </select>
                        </div>

                        <div id="requirementInput" class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">需求描述</label>
                            <textarea id="codeRequirement" rows="4"
                                      class="w-full border-2 border-gray-400 rounded-lg px-4 py-3 focus:ring-2 focus:ring-violet-200 focus:border-violet-500 hover:border-gray-500 transition-all duration-300 resize-none"
                                      placeholder="请描述您的代码需求..."></textarea>
                        </div>

                        <div id="codeInput" class="space-y-2 hidden">
                            <label class="block text-sm font-medium text-gray-700">代码内容</label>
                            <textarea id="codeContent" rows="8"
                                      class="w-full border-2 border-gray-400 rounded-lg px-4 py-3 focus:ring-2 focus:ring-violet-200 focus:border-violet-500 hover:border-gray-500 transition-all duration-300 resize-none font-mono text-sm"
                                      placeholder="请粘贴您的代码..."></textarea>
                        </div>

                        <button onclick="processCode()" id="processCodeButton" 
                                class="w-full bg-primary text-white py-2 rounded-md hover:bg-accent">
                            <i class="bi bi-magic"></i> 处理代码
                        </button>
                    </div>
                </div>

                <!-- Code Output -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">AI回复</h3>
                    
                    <div id="codeOutput" class="min-h-96 border border-gray-200 rounded-md p-4 bg-gray-50">
                        <div class="text-center text-gray-500 py-8">
                            <i class="bi bi-robot text-4xl mb-4"></i>
                            <p>AI助手的回复将显示在这里</p>
                        </div>
                    </div>

                    <div class="mt-4 flex space-x-2">
                        <button onclick="copyCodeOutput()" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                            <i class="bi bi-clipboard"></i> 复制
                        </button>
                        <button onclick="clearCodeOutput()" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                            <i class="bi bi-trash"></i> 清空
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Providers Tab -->
        <div id="providers-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900">AI提供商配置</h3>
                    <button onclick="showAddProviderModal()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                        <i class="bi bi-plus mr-2"></i>添加提供商
                    </button>
                </div>

                <div id="providersTable" class="overflow-x-auto">
                    <!-- Providers table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- History Tab -->
        <div id="history-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">对话历史</h3>
                
                <div id="historyList" class="space-y-4">
                    <!-- History will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Add Provider Modal -->
    <div id="addProviderModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900">添加AI提供商</h3>
                    <button onclick="closeAddProviderModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                
                <form id="addProviderForm" class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">提供商名称</label>
                            <input type="text" id="providerName" class="mt-1 block w-full border-2 border-gray-400 rounded-lg px-4 py-3 focus:ring-2 focus:ring-violet-200 focus:border-violet-500 hover:border-gray-500 transition-all duration-300" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">提供商类型</label>
                            <select id="providerType" class="mt-1 block w-full border-2 border-gray-400 rounded-lg px-4 py-3 focus:ring-2 focus:ring-violet-200 focus:border-violet-500 hover:border-gray-500 transition-all duration-300" required>
                                <option value="OPENAI">OpenAI</option>
                                <option value="BAIDU_WENXIN">百度文心一言</option>
                                <option value="ALIBABA_TONGYI">阿里通义千问</option>
                                <option value="TENCENT_HUNYUAN">腾讯混元</option>
                                <option value="CUSTOM">自定义</option>
                            </select>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">API Key</label>
                            <input type="password" id="apiKey" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Base URL</label>
                            <input type="url" id="baseUrl" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">模型</label>
                            <input type="text" id="model" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">最大Token数</label>
                            <input type="number" id="maxTokens" value="2048" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Temperature</label>
                            <input type="number" id="temperature" value="0.7" step="0.1" min="0" max="2" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" id="isDefault" class="rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">设为默认</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700">描述</label>
                        <textarea id="description" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddProviderModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            取消
                        </button>
                        <button type="button" onclick="testProviderConnection()" class="bg-warning text-white px-4 py-2 rounded-md hover:bg-yellow-600">
                            测试连接
                        </button>
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            添加
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script>
        let currentConversationId = null;
        let currentProviderId = null;

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            // Load tab content
            if (tabName === 'chat') {
                loadConversations();
                loadAiProviders();
            } else if (tabName === 'code') {
                loadAiProviders();
                updateCodeInputVisibility();
            } else if (tabName === 'providers') {
                loadProviders();
            } else if (tabName === 'history') {
                loadHistory();
            }
        }

        // Create new conversation
        function createNewConversation() {
            const conversationId = 'conv_' + Date.now();
            currentConversationId = conversationId;

            // Clear chat messages
            document.getElementById('chatMessages').innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="bi bi-chat-dots text-4xl mb-4"></i>
                    <p>开始新的对话</p>
                </div>
            `;

            // Add to conversation list
            const conversationsList = document.getElementById('conversationsList');
            const newConversation = document.createElement('div');
            newConversation.className = 'conversation-item active';
            newConversation.innerHTML = `
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-medium text-gray-900">新对话</h4>
                        <p class="text-sm text-gray-500">刚刚创建</p>
                    </div>
                    <i class="bi bi-chat text-violet-600"></i>
                </div>
            `;

            // Remove active from other conversations
            conversationsList.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });

            conversationsList.insertBefore(newConversation, conversationsList.firstChild);
            showToast('新对话已创建', 'success');
        }

        // Send message
        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();

            if (!message) {
                showToast('请输入消息内容', 'warning');
                return;
            }

            if (!currentConversationId) {
                createNewConversation();
            }

            const chatMessages = document.getElementById('chatMessages');

            // Clear welcome message if exists
            if (chatMessages.querySelector('.text-center')) {
                chatMessages.innerHTML = '';
            }

            // Add user message
            const userMessage = document.createElement('div');
            userMessage.className = 'flex justify-end mb-4';
            userMessage.innerHTML = `
                <div class="message-user">
                    ${message}
                </div>
            `;
            chatMessages.appendChild(userMessage);

            // Clear input
            messageInput.value = '';

            // Simulate AI response
            setTimeout(() => {
                const aiMessage = document.createElement('div');
                aiMessage.className = 'flex justify-start mb-4';
                aiMessage.innerHTML = `
                    <div class="message-ai">
                        我理解您的问题。这是一个模拟的AI回复。在实际应用中，这里会调用真实的AI服务来生成回复。
                    </div>
                `;
                chatMessages.appendChild(aiMessage);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }, 1000);

            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Show add provider modal
        function showAddProviderModal() {
            document.getElementById('addProviderModal').classList.remove('hidden');
        }

        // Close add provider modal
        function closeAddProviderModal() {
            document.getElementById('addProviderModal').classList.add('hidden');
            document.getElementById('addProviderForm').reset();
        }

        // Test provider connection
        function testProviderConnection() {
            const formData = {
                name: document.getElementById('providerName').value,
                type: document.getElementById('providerType').value,
                apiKey: document.getElementById('apiKey').value,
                endpoint: document.getElementById('endpoint').value
            };

            console.log('Testing provider connection:', formData);
            showToast('AI提供商连接测试成功！', 'success');
        }

        // Load conversations
        function loadConversations() {
            // Mock data
            const conversationsList = document.getElementById('conversationsList');
            conversationsList.innerHTML = `
                <div class="conversation-item">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-gray-900">代码优化讨论</h4>
                            <p class="text-sm text-gray-500">2小时前</p>
                        </div>
                        <i class="bi bi-code text-violet-600"></i>
                    </div>
                </div>
                <div class="conversation-item">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-gray-900">API设计咨询</h4>
                            <p class="text-sm text-gray-500">昨天</p>
                        </div>
                        <i class="bi bi-cloud text-violet-600"></i>
                    </div>
                </div>
            `;
        }

        // Load AI providers
        function loadAiProviders() {
            // Mock data for provider selection
            const providerSelects = document.querySelectorAll('#aiProvider, #codeAiProvider');
            providerSelects.forEach(select => {
                select.innerHTML = `
                    <option value="">选择AI提供商</option>
                    <option value="openai">OpenAI GPT-4</option>
                    <option value="claude">Anthropic Claude</option>
                    <option value="gemini">Google Gemini</option>
                `;
            });
        }

        // Load providers
        function loadProviders() {
            // Implementation for providers tab
        }

        // Load history
        function loadHistory() {
            // Implementation for history tab
        }

        // Update code input visibility
        function updateCodeInputVisibility() {
            // Implementation for code tab
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            showTab('chat');

            // Handle message input enter key
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Handle add provider form
            document.getElementById('addProviderForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = {
                    name: document.getElementById('providerName').value,
                    type: document.getElementById('providerType').value,
                    apiKey: document.getElementById('apiKey').value,
                    endpoint: document.getElementById('endpoint').value,
                    description: document.getElementById('description').value,
                    isDefault: document.getElementById('isDefault').checked
                };

                console.log('Adding AI provider:', formData);
                showToast('AI提供商添加成功！', 'success');
                closeAddProviderModal();
                loadProviders();
            });
        });
    </script>

    <style>
        .tab-button {
            @apply flex items-center px-4 py-2 rounded-xl font-medium text-sm text-gray-600 hover:text-gray-900 hover:bg-white/50 transition-all duration-200;
        }

        .tab-button.active {
            @apply bg-white text-violet-600 shadow-md;
        }

        .message-user {
            @apply bg-gradient-to-r from-violet-600 to-purple-600 text-white ml-auto max-w-xs lg:max-w-md px-4 py-3 rounded-2xl rounded-br-md shadow-lg;
        }

        .message-ai {
            @apply bg-white/80 backdrop-blur-sm text-gray-900 mr-auto max-w-xs lg:max-w-md px-4 py-3 rounded-2xl rounded-bl-md shadow-lg border border-white/20;
        }

        .chat-container {
            @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20;
        }

        .conversation-item {
            @apply p-3 rounded-lg hover:bg-violet-50 cursor-pointer transition-colors duration-200 border border-transparent;
        }

        .conversation-item:hover {
            @apply border-violet-200;
        }

        .conversation-item.active {
            @apply bg-violet-100 border-violet-300;
        }

        .code-editor {
            @apply bg-gray-50 border-2 border-gray-200 rounded-lg p-4 font-mono text-sm focus:border-violet-500 focus:ring-2 focus:ring-violet-200 transition-all duration-200;
        }

        .ai-provider-card {
            @apply bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:shadow-lg transition-all duration-300;
        }
    </style>
</body>
</html>
