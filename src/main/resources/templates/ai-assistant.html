<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能助手 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3498db',
                        secondary: '#2c3e50',
                        accent: '#2980b9',
                        success: '#2ecc71',
                        warning: '#f1c40f',
                        danger: '#e74c3c'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-semibold text-gray-900">
                        <i class="bi bi-arrow-left mr-2"></i>返回主页
                    </a>
                </div>
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">AI智能助手</h1>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Tabs -->
        <div class="mb-6">
            <nav class="flex space-x-8" aria-label="Tabs">
                <button id="tab-chat" class="tab-button active" onclick="showTab('chat')">
                    <i class="bi bi-chat-dots mr-2"></i>智能对话
                </button>
                <button id="tab-code" class="tab-button" onclick="showTab('code')">
                    <i class="bi bi-code-slash mr-2"></i>代码助手
                </button>
                <button id="tab-providers" class="tab-button" onclick="showTab('providers')">
                    <i class="bi bi-gear mr-2"></i>AI配置
                </button>
                <button id="tab-history" class="tab-button" onclick="showTab('history')">
                    <i class="bi bi-clock-history mr-2"></i>对话历史
                </button>
            </nav>
        </div>

        <!-- Chat Tab -->
        <div id="chat-tab" class="tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Conversation List -->
                <div class="lg:col-span-1">
                    <div class="bg-white shadow rounded-lg p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">对话列表</h3>
                            <button onclick="createNewConversation()" class="bg-primary text-white px-3 py-1 rounded text-sm hover:bg-accent">
                                <i class="bi bi-plus"></i> 新对话
                            </button>
                        </div>
                        <div id="conversationList" class="space-y-2">
                            <!-- Conversations will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Chat Area -->
                <div class="lg:col-span-3">
                    <div class="bg-white shadow rounded-lg h-96 flex flex-col">
                        <!-- Chat Header -->
                        <div class="border-b p-4">
                            <div class="flex justify-between items-center">
                                <h3 id="chatTitle" class="text-lg font-medium text-gray-900">选择或创建对话</h3>
                                <div class="flex space-x-2">
                                    <select id="aiProviderSelect" class="text-sm border-gray-300 rounded-md">
                                        <option value="">选择AI提供商</option>
                                    </select>
                                    <button onclick="clearChat()" class="text-gray-500 hover:text-gray-700">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Messages Area -->
                        <div id="messagesArea" class="flex-1 overflow-y-auto p-4 space-y-4">
                            <div class="text-center text-gray-500 py-8">
                                <i class="bi bi-chat-dots text-4xl mb-4"></i>
                                <p>开始与AI助手对话</p>
                            </div>
                        </div>

                        <!-- Input Area -->
                        <div class="border-t p-4">
                            <div class="flex space-x-2">
                                <input type="text" id="messageInput" placeholder="输入您的问题..." 
                                       class="flex-1 border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                                       onkeypress="handleKeyPress(event)">
                                <button onclick="sendMessage()" id="sendButton" 
                                        class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent disabled:opacity-50">
                                    <i class="bi bi-send"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Code Assistant Tab -->
        <div id="code-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Code Input -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">代码助手</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">功能类型</label>
                            <select id="codeFunction" class="w-full border-gray-300 rounded-md">
                                <option value="generate">代码生成</option>
                                <option value="review">代码审查</option>
                                <option value="explain">代码解释</option>
                                <option value="document">生成文档</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">编程语言</label>
                            <select id="codeLanguage" class="w-full border-gray-300 rounded-md">
                                <option value="java">Java</option>
                                <option value="javascript">JavaScript</option>
                                <option value="python">Python</option>
                                <option value="typescript">TypeScript</option>
                                <option value="go">Go</option>
                                <option value="rust">Rust</option>
                                <option value="cpp">C++</option>
                            </select>
                        </div>

                        <div id="requirementInput" class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">需求描述</label>
                            <textarea id="codeRequirement" rows="4" 
                                      class="w-full border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                                      placeholder="请描述您的代码需求..."></textarea>
                        </div>

                        <div id="codeInput" class="space-y-2 hidden">
                            <label class="block text-sm font-medium text-gray-700">代码内容</label>
                            <textarea id="codeContent" rows="8" 
                                      class="w-full border-gray-300 rounded-md focus:ring-primary focus:border-primary font-mono text-sm"
                                      placeholder="请粘贴您的代码..."></textarea>
                        </div>

                        <button onclick="processCode()" id="processCodeButton" 
                                class="w-full bg-primary text-white py-2 rounded-md hover:bg-accent">
                            <i class="bi bi-magic"></i> 处理代码
                        </button>
                    </div>
                </div>

                <!-- Code Output -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">AI回复</h3>
                    
                    <div id="codeOutput" class="min-h-96 border border-gray-200 rounded-md p-4 bg-gray-50">
                        <div class="text-center text-gray-500 py-8">
                            <i class="bi bi-robot text-4xl mb-4"></i>
                            <p>AI助手的回复将显示在这里</p>
                        </div>
                    </div>

                    <div class="mt-4 flex space-x-2">
                        <button onclick="copyCodeOutput()" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                            <i class="bi bi-clipboard"></i> 复制
                        </button>
                        <button onclick="clearCodeOutput()" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                            <i class="bi bi-trash"></i> 清空
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Providers Tab -->
        <div id="providers-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900">AI提供商配置</h3>
                    <button onclick="showAddProviderModal()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                        <i class="bi bi-plus mr-2"></i>添加提供商
                    </button>
                </div>

                <div id="providersTable" class="overflow-x-auto">
                    <!-- Providers table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- History Tab -->
        <div id="history-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">对话历史</h3>
                
                <div id="historyList" class="space-y-4">
                    <!-- History will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Add Provider Modal -->
    <div id="addProviderModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900">添加AI提供商</h3>
                    <button onclick="closeAddProviderModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                
                <form id="addProviderForm" class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">提供商名称</label>
                            <input type="text" id="providerName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">提供商类型</label>
                            <select id="providerType" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                                <option value="OPENAI">OpenAI</option>
                                <option value="BAIDU_WENXIN">百度文心一言</option>
                                <option value="ALIBABA_TONGYI">阿里通义千问</option>
                                <option value="TENCENT_HUNYUAN">腾讯混元</option>
                                <option value="CUSTOM">自定义</option>
                            </select>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">API Key</label>
                            <input type="password" id="apiKey" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Base URL</label>
                            <input type="url" id="baseUrl" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">模型</label>
                            <input type="text" id="model" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">最大Token数</label>
                            <input type="number" id="maxTokens" value="2048" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Temperature</label>
                            <input type="number" id="temperature" value="0.7" step="0.1" min="0" max="2" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" id="isDefault" class="rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">设为默认</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700">描述</label>
                        <textarea id="description" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddProviderModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            取消
                        </button>
                        <button type="button" onclick="testProviderConnection()" class="bg-warning text-white px-4 py-2 rounded-md hover:bg-yellow-600">
                            测试连接
                        </button>
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            添加
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script>
        let currentConversationId = null;
        let currentProviderId = null;

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            // Load tab content
            if (tabName === 'chat') {
                loadConversations();
                loadAiProviders();
            } else if (tabName === 'code') {
                loadAiProviders();
                updateCodeInputVisibility();
            } else if (tabName === 'providers') {
                loadProviders();
            } else if (tabName === 'history') {
                loadHistory();
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            showTab('chat');
        });
    </script>

    <style>
        .tab-button {
            @apply py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300;
        }
        
        .tab-button.active {
            @apply border-primary text-primary;
        }

        .message-user {
            @apply bg-primary text-white ml-auto max-w-xs lg:max-w-md px-4 py-2 rounded-lg;
        }

        .message-ai {
            @apply bg-gray-200 text-gray-900 mr-auto max-w-xs lg:max-w-md px-4 py-2 rounded-lg;
        }
    </style>
</body>
</html>
