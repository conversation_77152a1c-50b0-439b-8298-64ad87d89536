<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3498db',
                        secondary: '#2c3e50',
                        accent: '#2980b9',
                        success: '#2ecc71',
                        warning: '#f1c40f',
                        danger: '#e74c3c'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-semibold text-gray-900">
                        <i class="bi bi-arrow-left mr-2"></i>返回主页
                    </a>
                </div>
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">系统监控</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="systemStatus" class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-600">系统正常</span>
                    </div>
                    <button onclick="refreshData()" class="text-gray-500 hover:text-gray-700">
                        <i class="bi bi-arrow-clockwise text-lg"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- System Health Overview -->
        <div class="mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">系统健康状态</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- CPU Usage -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">CPU使用率</p>
                            <p id="cpuUsage" class="text-2xl font-bold text-gray-900">--</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="bi bi-cpu text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="cpuProgress" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Memory Usage -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">内存使用率</p>
                            <p id="memoryUsage" class="text-2xl font-bold text-gray-900">--</p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="bi bi-memory text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="memoryProgress" class="bg-green-600 h-2 rounded-full" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Active Alerts -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">活跃告警</p>
                            <p id="activeAlerts" class="text-2xl font-bold text-gray-900">--</p>
                        </div>
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="bi bi-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="#alerts" class="text-sm text-red-600 hover:text-red-800">查看详情 →</a>
                    </div>
                </div>

                <!-- System Uptime -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">系统运行时间</p>
                            <p id="systemUptime" class="text-2xl font-bold text-gray-900">--</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="bi bi-clock text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <p id="lastUpdate" class="text-sm text-gray-500">最后更新: --</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-6">
            <nav class="flex space-x-8" aria-label="Tabs">
                <button id="tab-metrics" class="tab-button active" onclick="showTab('metrics')">
                    <i class="bi bi-graph-up mr-2"></i>系统指标
                </button>
                <button id="tab-alerts" class="tab-button" onclick="showTab('alerts')">
                    <i class="bi bi-bell mr-2"></i>告警管理
                </button>
                <button id="tab-rules" class="tab-button" onclick="showTab('rules')">
                    <i class="bi bi-gear mr-2"></i>告警规则
                </button>
                <button id="tab-notifications" class="tab-button" onclick="showTab('notifications')">
                    <i class="bi bi-envelope mr-2"></i>通知设置
                </button>
            </nav>
        </div>

        <!-- Metrics Tab -->
        <div id="metrics-tab" class="tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- CPU Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">CPU使用率趋势</h3>
                    <canvas id="cpuChart" width="400" height="200"></canvas>
                </div>

                <!-- Memory Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">内存使用率趋势</h3>
                    <canvas id="memoryChart" width="400" height="200"></canvas>
                </div>

                <!-- Metrics Table -->
                <div class="lg:col-span-2 bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">实时指标</h3>
                        <div class="flex space-x-2">
                            <select id="metricFilter" class="text-sm border-gray-300 rounded-md">
                                <option value="">所有指标</option>
                                <option value="CPU_USAGE">CPU使用率</option>
                                <option value="MEMORY_USAGE">内存使用率</option>
                                <option value="DISK_USAGE">磁盘使用率</option>
                                <option value="CUSTOM">自定义指标</option>
                            </select>
                            <button onclick="exportMetrics()" class="text-sm bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">
                                导出数据
                            </button>
                        </div>
                    </div>
                    <div id="metricsTable" class="overflow-x-auto">
                        <!-- Metrics table will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts Tab -->
        <div id="alerts-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900">告警记录</h3>
                    <div class="flex space-x-3">
                        <select id="alertStatusFilter" class="text-sm border-gray-300 rounded-md">
                            <option value="">所有状态</option>
                            <option value="OPEN">未处理</option>
                            <option value="ACKNOWLEDGED">已确认</option>
                            <option value="RESOLVED">已解决</option>
                            <option value="CLOSED">已关闭</option>
                        </select>
                        <select id="alertSeverityFilter" class="text-sm border-gray-300 rounded-md">
                            <option value="">所有级别</option>
                            <option value="INFO">信息</option>
                            <option value="WARNING">警告</option>
                            <option value="ERROR">错误</option>
                            <option value="CRITICAL">严重</option>
                        </select>
                    </div>
                </div>

                <div id="alertsTable" class="overflow-x-auto">
                    <!-- Alerts table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Rules Tab -->
        <div id="rules-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900">告警规则</h3>
                    <button onclick="showCreateRuleModal()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                        <i class="bi bi-plus mr-2"></i>创建规则
                    </button>
                </div>

                <div id="rulesTable" class="overflow-x-auto">
                    <!-- Rules table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Notifications Tab -->
        <div id="notifications-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Notification Channels -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">通知渠道</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">邮箱通知</label>
                            <div class="flex space-x-2">
                                <input type="email" id="emailChannel" placeholder="<EMAIL>" 
                                       class="flex-1 border-gray-300 rounded-md text-sm">
                                <button onclick="testNotification('email')" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                                    测试
                                </button>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Webhook通知</label>
                            <div class="flex space-x-2">
                                <input type="url" id="webhookChannel" placeholder="https://api.example.com/webhook" 
                                       class="flex-1 border-gray-300 rounded-md text-sm">
                                <button onclick="testNotification('webhook')" class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600">
                                    测试
                                </button>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Slack通知</label>
                            <div class="flex space-x-2">
                                <input type="url" id="slackChannel" placeholder="https://hooks.slack.com/..." 
                                       class="flex-1 border-gray-300 rounded-md text-sm">
                                <button onclick="testNotification('slack')" class="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600">
                                    测试
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notification History -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">通知历史</h3>
                    <div id="notificationHistory" class="space-y-3">
                        <!-- Notification history will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Alert Rule Modal -->
    <div id="createRuleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900">创建告警规则</h3>
                    <button onclick="closeCreateRuleModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                
                <form id="createRuleForm" class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">规则名称</label>
                            <input type="text" id="ruleName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">指标名称</label>
                            <select id="metricName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                                <option value="">选择指标</option>
                                <option value="cpu.usage">CPU使用率</option>
                                <option value="memory.heap.usage">内存使用率</option>
                                <option value="disk.usage">磁盘使用率</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">比较操作符</label>
                            <select id="operator" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                                <option value="GREATER_THAN">大于</option>
                                <option value="GREATER_THAN_OR_EQUAL">大于等于</option>
                                <option value="LESS_THAN">小于</option>
                                <option value="LESS_THAN_OR_EQUAL">小于等于</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">阈值</label>
                            <input type="number" id="threshold" step="0.01" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">严重程度</label>
                            <select id="severity" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                                <option value="INFO">信息</option>
                                <option value="WARNING">警告</option>
                                <option value="ERROR">错误</option>
                                <option value="CRITICAL">严重</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">持续时间（分钟）</label>
                            <input type="number" id="duration" value="5" min="1" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">描述</label>
                            <textarea id="ruleDescription" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">通知渠道</label>
                            <input type="text" id="notificationChannels" placeholder="email:<EMAIL>,slack:webhook_url" 
                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                            <p class="mt-1 text-sm text-gray-500">多个渠道用逗号分隔，格式：类型:地址</p>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeCreateRuleModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            取消
                        </button>
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            创建
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script>
        let cpuChart, memoryChart;
        let refreshInterval;

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            // Load tab content
            if (tabName === 'metrics') {
                loadMetrics();
                initCharts();
            } else if (tabName === 'alerts') {
                loadAlerts();
            } else if (tabName === 'rules') {
                loadAlertRules();
            } else if (tabName === 'notifications') {
                loadNotificationHistory();
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            showTab('metrics');
            loadSystemHealth();
            
            // Start auto refresh
            refreshInterval = setInterval(() => {
                loadSystemHealth();
                if (document.getElementById('metrics-tab').classList.contains('hidden') === false) {
                    loadMetrics();
                    updateCharts();
                }
            }, 30000); // 30 seconds
        });

        // Load system health status
        function loadSystemHealth() {
            fetch('/api/monitoring/health')
                .then(response => response.json())
                .then(data => {
                    updateHealthDisplay(data);
                })
                .catch(error => {
                    console.error('加载系统健康状态失败:', error);
                });
        }

        // Update health display
        function updateHealthDisplay(health) {
            // Update CPU usage
            if (health.cpuUsage !== undefined) {
                document.getElementById('cpuUsage').textContent = health.cpuUsage.toFixed(1) + '%';
                document.getElementById('cpuProgress').style.width = health.cpuUsage + '%';
                
                // Update progress bar color based on usage
                const cpuProgress = document.getElementById('cpuProgress');
                if (health.cpuUsage > 90) {
                    cpuProgress.className = 'bg-red-600 h-2 rounded-full';
                } else if (health.cpuUsage > 70) {
                    cpuProgress.className = 'bg-yellow-600 h-2 rounded-full';
                } else {
                    cpuProgress.className = 'bg-blue-600 h-2 rounded-full';
                }
            }

            // Update Memory usage
            if (health.memoryUsage !== undefined) {
                document.getElementById('memoryUsage').textContent = health.memoryUsage.toFixed(1) + '%';
                document.getElementById('memoryProgress').style.width = health.memoryUsage + '%';
                
                // Update progress bar color based on usage
                const memoryProgress = document.getElementById('memoryProgress');
                if (health.memoryUsage > 90) {
                    memoryProgress.className = 'bg-red-600 h-2 rounded-full';
                } else if (health.memoryUsage > 70) {
                    memoryProgress.className = 'bg-yellow-600 h-2 rounded-full';
                } else {
                    memoryProgress.className = 'bg-green-600 h-2 rounded-full';
                }
            }

            // Update active alerts
            if (health.activeAlerts !== undefined) {
                document.getElementById('activeAlerts').textContent = health.activeAlerts;
            }

            // Update system status
            const statusElement = document.getElementById('systemStatus');
            const statusDot = statusElement.querySelector('div');
            const statusText = statusElement.querySelector('span');
            
            switch (health.overallStatus) {
                case 'HEALTHY':
                    statusDot.className = 'w-3 h-3 bg-green-500 rounded-full mr-2';
                    statusText.textContent = '系统正常';
                    break;
                case 'WARNING':
                    statusDot.className = 'w-3 h-3 bg-yellow-500 rounded-full mr-2';
                    statusText.textContent = '系统警告';
                    break;
                case 'CRITICAL':
                    statusDot.className = 'w-3 h-3 bg-red-500 rounded-full mr-2';
                    statusText.textContent = '系统异常';
                    break;
            }

            // Update last update time
            document.getElementById('lastUpdate').textContent = '最后更新: ' + new Date().toLocaleTimeString();
        }

        // Initialize charts
        function initCharts() {
            if (cpuChart) cpuChart.destroy();
            if (memoryChart) memoryChart.destroy();

            // CPU Chart
            const cpuCtx = document.getElementById('cpuChart').getContext('2d');
            cpuChart = new Chart(cpuCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU使用率 (%)',
                        data: [],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            // Memory Chart
            const memoryCtx = document.getElementById('memoryChart').getContext('2d');
            memoryChart = new Chart(memoryCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '内存使用率 (%)',
                        data: [],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            // Load initial chart data
            updateCharts();
        }

        // Refresh all data
        function refreshData() {
            loadSystemHealth();
            const activeTab = document.querySelector('.tab-button.active').id.replace('tab-', '');
            showTab(activeTab);
            showToast('数据已刷新', 'success');
        }
    </script>

    <style>
        .tab-button {
            @apply py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300;
        }
        
        .tab-button.active {
            @apply border-primary text-primary;
        }

        .alert-severity-info { @apply bg-blue-100 text-blue-800; }
        .alert-severity-warning { @apply bg-yellow-100 text-yellow-800; }
        .alert-severity-error { @apply bg-orange-100 text-orange-800; }
        .alert-severity-critical { @apply bg-red-100 text-red-800; }

        .alert-status-open { @apply bg-red-100 text-red-800; }
        .alert-status-acknowledged { @apply bg-yellow-100 text-yellow-800; }
        .alert-status-resolved { @apply bg-green-100 text-green-800; }
        .alert-status-closed { @apply bg-gray-100 text-gray-800; }
    </style>
</body>
</html>
