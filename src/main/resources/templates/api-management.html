<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API管理 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1e293b',
                        accent: '#2563eb',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-slate-50 via-teal-50 to-cyan-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white/95 backdrop-blur-sm shadow-lg border-b border-white/20 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="/" class="flex items-center space-x-3 text-gray-900 hover:text-teal-600 transition-colors">
                        <div class="w-8 h-8 bg-gradient-to-r from-teal-600 to-cyan-600 rounded-lg flex items-center justify-center">
                            <i class="bi bi-arrow-left text-white text-sm"></i>
                        </div>
                        <span class="font-semibold">返回主页</span>
                    </a>
                </div>
                <div class="flex items-center">
                    <div class="text-center">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-teal-600 to-cyan-600 bg-clip-text text-transparent">API管理</h1>
                        <p class="text-xs text-gray-500">API Management System</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="refreshData()" class="p-2 rounded-lg bg-teal-50 text-teal-600 hover:bg-teal-100 transition-colors">
                        <i class="bi bi-arrow-clockwise text-lg"></i>
                    </button>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">API监控</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Statistics Cards -->
        <div class="mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">API统计</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Total APIs -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">总API数</p>
                            <p id="totalApis" class="text-2xl font-bold text-gray-900">--</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="bi bi-cloud text-blue-600 text-xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Active APIs -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">活跃API</p>
                            <p id="activeApis" class="text-2xl font-bold text-green-600">--</p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="bi bi-check-circle text-green-600 text-xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Test Records -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">测试记录</p>
                            <p id="testRecords" class="text-2xl font-bold text-purple-600">--</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="bi bi-play-circle text-purple-600 text-xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Success Rate -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">成功率</p>
                            <p id="successRate" class="text-2xl font-bold text-orange-600">--</p>
                        </div>
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="bi bi-graph-up text-orange-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-6">
            <nav class="flex space-x-8" aria-label="Tabs">
                <button id="tab-documents" class="tab-button active" onclick="showTab('documents')">
                    <i class="bi bi-file-text mr-2"></i>API文档
                </button>
                <button id="tab-testing" class="tab-button" onclick="showTab('testing')">
                    <i class="bi bi-play-circle mr-2"></i>API测试
                </button>
                <button id="tab-monitoring" class="tab-button" onclick="showTab('monitoring')">
                    <i class="bi bi-graph-up mr-2"></i>性能监控
                </button>
                <button id="tab-settings" class="tab-button" onclick="showTab('settings')">
                    <i class="bi bi-gear mr-2"></i>设置
                </button>
            </nav>
        </div>

        <!-- API Documents Tab -->
        <div id="documents-tab" class="tab-content">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900">API文档</h3>
                    <div class="flex space-x-3">
                        <select id="statusFilter" class="text-sm border-gray-300 rounded-md">
                            <option value="">所有状态</option>
                            <option value="ACTIVE">活跃</option>
                            <option value="DEPRECATED">已废弃</option>
                            <option value="BETA">测试版</option>
                        </select>
                        <select id="methodFilter" class="text-sm border-gray-300 rounded-md">
                            <option value="">所有方法</option>
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PUT">PUT</option>
                            <option value="DELETE">DELETE</option>
                        </select>
                        <button onclick="createApiDoc()" class="bg-primary text-white px-4 py-2 rounded text-sm hover:bg-accent">
                            <i class="bi bi-plus mr-1"></i>新建API
                        </button>
                    </div>
                </div>

                <div id="apiDocsTable" class="overflow-x-auto">
                    <!-- API documents table will be loaded here -->
                    <div class="text-center py-8 text-gray-500">
                        <i class="bi bi-hourglass-split text-3xl mb-2"></i>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Testing Tab -->
        <div id="testing-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Test Form -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">API测试</h3>
                    
                    <form id="apiTestForm">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">请求方法</label>
                                <select id="testMethod" class="mt-1 block w-full border-gray-300 rounded-md">
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="DELETE">DELETE</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700">请求URL</label>
                                <input type="url" id="testUrl" placeholder="https://api.example.com/endpoint" 
                                       class="mt-1 block w-full border-gray-300 rounded-md">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700">请求头</label>
                                <textarea id="testHeaders" rows="3" placeholder="Content-Type: application/json" 
                                          class="mt-1 block w-full border-gray-300 rounded-md"></textarea>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700">请求体</label>
                                <textarea id="testBody" rows="4" placeholder='{"key": "value"}' 
                                          class="mt-1 block w-full border-gray-300 rounded-md"></textarea>
                            </div>
                            
                            <button type="submit" class="w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-accent">
                                <i class="bi bi-play mr-2"></i>发送请求
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Test Results -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">测试结果</h3>
                    
                    <div id="testResults" class="space-y-4">
                        <div class="text-center py-8 text-gray-500">
                            <i class="bi bi-play-circle text-3xl mb-2"></i>
                            <p>点击"发送请求"开始测试</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Monitoring Tab -->
        <div id="monitoring-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Response Time Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">响应时间趋势</h3>
                    <canvas id="responseTimeChart" width="400" height="200"></canvas>
                </div>

                <!-- Success Rate Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">成功率统计</h3>
                    <canvas id="successRateChart" width="400" height="200"></canvas>
                </div>

                <!-- API Performance Table -->
                <div class="lg:col-span-2 bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">API性能排行</h3>
                    <div id="performanceTable">
                        <!-- Performance table will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">API管理设置</h3>
                
                <div class="space-y-6">
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">默认配置</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">默认超时时间（秒）</label>
                                <input type="number" value="30" class="mt-1 block w-full border-gray-300 rounded-md">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">重试次数</label>
                                <input type="number" value="3" class="mt-1 block w-full border-gray-300 rounded-md">
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">通知设置</h4>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="rounded border-gray-300 text-primary">
                                <span class="ml-2 text-sm text-gray-700">API测试失败时发送通知</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-primary">
                                <span class="ml-2 text-sm text-gray-700">API响应时间超过阈值时告警</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="pt-4">
                        <button class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            保存设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script>
        let responseTimeChart, successRateChart;

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            // Load tab content
            if (tabName === 'documents') {
                loadApiDocuments();
            } else if (tabName === 'testing') {
                // Testing tab is ready
            } else if (tabName === 'monitoring') {
                loadMonitoring();
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            loadStatistics();
            showTab('documents');
            
            // Setup API test form
            document.getElementById('apiTestForm').addEventListener('submit', handleApiTest);
        });

        // Load statistics
        function loadStatistics() {
            // Mock data - replace with actual API calls
            document.getElementById('totalApis').textContent = '45';
            document.getElementById('activeApis').textContent = '42';
            document.getElementById('testRecords').textContent = '128';
            document.getElementById('successRate').textContent = '98.5%';
        }

        // Load API documents
        function loadApiDocuments() {
            const container = document.getElementById('apiDocsTable');
            container.innerHTML = `
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">方法</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路径</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后测试</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">用户登录</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">POST</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">/api/auth/login</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">活跃</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 10:30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-green-600 hover:text-green-900">测试</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">获取用户信息</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">GET</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">/api/users/{id}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">活跃</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 09:45</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-green-600 hover:text-green-900">测试</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            `;
        }

        // Handle API test
        function handleApiTest(e) {
            e.preventDefault();
            
            const method = document.getElementById('testMethod').value;
            const url = document.getElementById('testUrl').value;
            const headers = document.getElementById('testHeaders').value;
            const body = document.getElementById('testBody').value;
            
            if (!url) {
                showToast('请输入请求URL', 'error');
                return;
            }
            
            // Show loading
            const resultsContainer = document.getElementById('testResults');
            resultsContainer.innerHTML = `
                <div class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                    <p class="text-gray-500">正在发送请求...</p>
                </div>
            `;
            
            // Simulate API call
            setTimeout(() => {
                const mockResponse = {
                    status: 200,
                    statusText: 'OK',
                    responseTime: Math.floor(Math.random() * 500) + 100,
                    data: {
                        success: true,
                        message: '请求成功',
                        data: { id: 1, name: 'Test User' }
                    }
                };
                
                displayTestResults(mockResponse);
                showToast('API测试完成', 'success');
            }, 2000);
        }

        // Display test results
        function displayTestResults(response) {
            const container = document.getElementById('testResults');
            container.innerHTML = `
                <div class="space-y-4">
                    <div>
                        <h4 class="font-medium text-gray-900">响应状态</h4>
                        <div class="mt-1 flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-semibold rounded-full ${response.status === 200 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                ${response.status} ${response.statusText}
                            </span>
                            <span class="text-sm text-gray-500">响应时间: ${response.responseTime}ms</span>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="font-medium text-gray-900">响应数据</h4>
                        <pre class="mt-1 bg-gray-50 p-3 rounded text-sm overflow-x-auto">${JSON.stringify(response.data, null, 2)}</pre>
                    </div>
                </div>
            `;
        }

        // Load monitoring data
        function loadMonitoring() {
            initCharts();
        }

        // Initialize charts
        function initCharts() {
            // Response Time Chart
            const responseTimeCtx = document.getElementById('responseTimeChart').getContext('2d');
            responseTimeChart = new Chart(responseTimeCtx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                    datasets: [{
                        label: '平均响应时间 (ms)',
                        data: [120, 150, 180, 200, 170, 140],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Success Rate Chart
            const successRateCtx = document.getElementById('successRateChart').getContext('2d');
            successRateChart = new Chart(successRateCtx, {
                type: 'doughnut',
                data: {
                    labels: ['成功', '失败'],
                    datasets: [{
                        data: [98.5, 1.5],
                        backgroundColor: ['#10b981', '#ef4444']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Create API document
        function createApiDoc() {
            showToast('创建API文档功能开发中...', 'info');
        }

        // Refresh data
        function refreshData() {
            loadStatistics();
            const activeTab = document.querySelector('.tab-button.active').id.replace('tab-', '');
            showTab(activeTab);
            showToast('数据已刷新', 'success');
        }
    </script>

    <style>
        .tab-button {
            @apply py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300;
        }
        
        .tab-button.active {
            @apply border-primary text-primary;
        }
    </style>
</body>
</html>
