<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置管理 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3498db',
                        secondary: '#2c3e50',
                        accent: '#2980b9',
                        success: '#2ecc71',
                        warning: '#f1c40f',
                        danger: '#e74c3c'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-semibold text-gray-900">
                        <i class="bi bi-arrow-left mr-2"></i>返回主页
                    </a>
                </div>
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">配置管理</h1>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Tabs -->
        <div class="mb-6">
            <nav class="flex space-x-8" aria-label="Tabs">
                <button id="tab-configs" class="tab-button active" onclick="showTab('configs')">
                    <i class="bi bi-gear mr-2"></i>配置项
                </button>
                <button id="tab-environments" class="tab-button" onclick="showTab('environments')">
                    <i class="bi bi-layers mr-2"></i>环境管理
                </button>
                <button id="tab-import-export" class="tab-button" onclick="showTab('import-export')">
                    <i class="bi bi-arrow-down-up mr-2"></i>导入导出
                </button>
            </nav>
        </div>

        <!-- Configs Tab -->
        <div id="configs-tab" class="tab-content">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <div class="flex space-x-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目</label>
                            <select id="projectSelect" class="border-gray-300 rounded-md">
                                <option value="">系统级配置</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">环境</label>
                            <select id="environmentSelect" class="border-gray-300 rounded-md">
                                <option value="development">开发环境</option>
                                <option value="testing">测试环境</option>
                                <option value="staging">预发布环境</option>
                                <option value="production">生产环境</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">分类</label>
                            <select id="categoryFilter" class="border-gray-300 rounded-md">
                                <option value="">所有分类</option>
                                <option value="database">数据库</option>
                                <option value="cache">缓存</option>
                                <option value="security">安全</option>
                                <option value="logging">日志</option>
                                <option value="api">API</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <div class="relative">
                            <input type="text" id="configSearch" placeholder="搜索配置..." 
                                   class="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary">
                            <i class="bi bi-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <button onclick="showCreateConfigModal()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            <i class="bi bi-plus mr-2"></i>添加配置
                        </button>
                    </div>
                </div>

                <div id="configsTable" class="overflow-x-auto">
                    <!-- Configs table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Environments Tab -->
        <div id="environments-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Environment List -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">环境列表</h3>
                    <div id="environmentsList" class="space-y-3">
                        <!-- Environments will be loaded here -->
                    </div>
                </div>

                <!-- Environment Operations -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">环境操作</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">复制配置</label>
                            <div class="grid grid-cols-2 gap-2">
                                <select id="sourceEnv" class="border-gray-300 rounded-md">
                                    <option value="">源环境</option>
                                </select>
                                <select id="targetEnv" class="border-gray-300 rounded-md">
                                    <option value="">目标环境</option>
                                </select>
                            </div>
                            <button onclick="copyConfigs()" class="mt-2 w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600">
                                <i class="bi bi-copy mr-2"></i>复制配置
                            </button>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">环境对比</label>
                            <div class="grid grid-cols-2 gap-2">
                                <select id="compareEnv1" class="border-gray-300 rounded-md">
                                    <option value="">环境1</option>
                                </select>
                                <select id="compareEnv2" class="border-gray-300 rounded-md">
                                    <option value="">环境2</option>
                                </select>
                            </div>
                            <button onclick="compareEnvironments()" class="mt-2 w-full bg-purple-500 text-white py-2 rounded-md hover:bg-purple-600">
                                <i class="bi bi-arrow-left-right mr-2"></i>对比环境
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Import/Export Tab -->
        <div id="import-export-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Export -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">导出配置</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目</label>
                            <select id="exportProjectSelect" class="w-full border-gray-300 rounded-md">
                                <option value="">系统级配置</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">环境</label>
                            <select id="exportEnvironmentSelect" class="w-full border-gray-300 rounded-md">
                                <option value="development">开发环境</option>
                                <option value="testing">测试环境</option>
                                <option value="staging">预发布环境</option>
                                <option value="production">生产环境</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">格式</label>
                            <select id="exportFormat" class="w-full border-gray-300 rounded-md">
                                <option value="properties">Properties</option>
                                <option value="yaml">YAML</option>
                                <option value="json">JSON</option>
                            </select>
                        </div>
                        <button onclick="exportConfigs()" class="w-full bg-success text-white py-2 rounded-md hover:bg-green-600">
                            <i class="bi bi-download mr-2"></i>导出配置
                        </button>
                    </div>
                </div>

                <!-- Import -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">导入配置</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">项目</label>
                            <select id="importProjectSelect" class="w-full border-gray-300 rounded-md">
                                <option value="">系统级配置</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">环境</label>
                            <select id="importEnvironmentSelect" class="w-full border-gray-300 rounded-md">
                                <option value="development">开发环境</option>
                                <option value="testing">测试环境</option>
                                <option value="staging">预发布环境</option>
                                <option value="production">生产环境</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">配置内容</label>
                            <textarea id="importContent" rows="8" 
                                      class="w-full border-gray-300 rounded-md focus:ring-primary focus:border-primary font-mono text-sm"
                                      placeholder="粘贴配置内容..."></textarea>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="validateImportContent()" class="flex-1 bg-warning text-white py-2 rounded-md hover:bg-yellow-600">
                                <i class="bi bi-check-circle mr-2"></i>验证
                            </button>
                            <button onclick="importConfigs()" class="flex-1 bg-primary text-white py-2 rounded-md hover:bg-accent">
                                <i class="bi bi-upload mr-2"></i>导入
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Import/Export History -->
            <div class="mt-6 bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">操作历史</h3>
                <div id="operationHistory" class="space-y-3">
                    <!-- Operation history will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Create Config Modal -->
    <div id="createConfigModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900">添加配置项</h3>
                    <button onclick="closeCreateConfigModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                
                <form id="createConfigForm" class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">配置键</label>
                            <input type="text" id="configKey" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">配置类型</label>
                            <select id="configType" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                                <option value="STRING">字符串</option>
                                <option value="INTEGER">整数</option>
                                <option value="BOOLEAN">布尔值</option>
                                <option value="DECIMAL">小数</option>
                                <option value="JSON">JSON对象</option>
                                <option value="YAML">YAML配置</option>
                                <option value="PROPERTIES">Properties文件</option>
                                <option value="URL">URL地址</option>
                                <option value="EMAIL">邮箱地址</option>
                                <option value="PASSWORD">密码</option>
                                <option value="API_KEY">API密钥</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">环境</label>
                            <select id="configEnvironment" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                                <option value="development">开发环境</option>
                                <option value="testing">测试环境</option>
                                <option value="staging">预发布环境</option>
                                <option value="production">生产环境</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">分类</label>
                            <select id="configCategory" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                                <option value="">选择分类</option>
                                <option value="database">数据库</option>
                                <option value="cache">缓存</option>
                                <option value="security">安全</option>
                                <option value="logging">日志</option>
                                <option value="api">API</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">配置值</label>
                            <textarea id="configValue" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">默认值</label>
                            <input type="text" id="defaultValue" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">描述</label>
                            <textarea id="configDescription" rows="2" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" id="isRequired" class="rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">必填项</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" id="isEncrypted" class="rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">加密存储</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeCreateConfigModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            取消
                        </button>
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            添加
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script>
        let configs = [];
        let environments = [];
        let projects = [];

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            // Load tab content
            if (tabName === 'configs') {
                loadConfigs();
                loadProjects();
            } else if (tabName === 'environments') {
                loadEnvironments();
            } else if (tabName === 'import-export') {
                loadProjects();
                loadEnvironments();
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            showTab('configs');
            
            // Event listeners
            document.getElementById('projectSelect').addEventListener('change', loadConfigs);
            document.getElementById('environmentSelect').addEventListener('change', loadConfigs);
            document.getElementById('categoryFilter').addEventListener('change', loadConfigs);
            document.getElementById('configSearch').addEventListener('input', (e) => {
                const keyword = e.target.value.trim();
                searchConfigs(keyword);
            });
        });
    </script>

    <style>
        .tab-button {
            @apply py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300;
        }
        
        .tab-button.active {
            @apply border-primary text-primary;
        }

        .config-item {
            @apply border-b border-gray-200 py-3 hover:bg-gray-50;
        }

        .config-key {
            @apply font-mono text-sm font-medium text-gray-900;
        }

        .config-value {
            @apply font-mono text-sm text-gray-600;
        }

        .config-type-badge {
            @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
        }

        .type-string { @apply bg-blue-100 text-blue-800; }
        .type-integer { @apply bg-green-100 text-green-800; }
        .type-boolean { @apply bg-purple-100 text-purple-800; }
        .type-password { @apply bg-red-100 text-red-800; }
        .type-url { @apply bg-yellow-100 text-yellow-800; }
        .type-json { @apply bg-indigo-100 text-indigo-800; }
    </style>
</body>
</html>
