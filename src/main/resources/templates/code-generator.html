<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码生成器 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1e293b',
                        accent: '#2563eb',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white/95 backdrop-blur-sm shadow-lg border-b border-white/20 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="/" class="flex items-center space-x-3 text-gray-900 hover:text-blue-600 transition-colors">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="bi bi-arrow-left text-white text-sm"></i>
                        </div>
                        <span class="font-semibold">返回主页</span>
                    </a>
                </div>
                <div class="flex items-center">
                    <div class="text-center">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">代码生成器</h1>
                        <p class="text-xs text-gray-500">Code Generator</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">生成器就绪</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                <i class="bi bi-code-slash text-blue-600 mr-3"></i>
                智能代码生成平台
            </h2>
            <p class="text-gray-600">快速生成高质量的Spring Boot项目代码，提升开发效率</p>
        </div>

        <!-- Tabs -->
        <div class="mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg border border-white/20">
                <nav class="flex space-x-2" aria-label="Tabs">
                    <button id="tab-generator" class="tab-button active" onclick="showTab('generator')">
                        <i class="bi bi-code-slash mr-2"></i>代码生成
                    </button>
                    <button id="tab-templates" class="tab-button" onclick="showTab('templates')">
                        <i class="bi bi-file-earmark-code mr-2"></i>模板管理
                    </button>
                    <button id="tab-tasks" class="tab-button" onclick="showTab('tasks')">
                        <i class="bi bi-list-task mr-2"></i>任务管理
                    </button>
                </nav>
            </div>
        </div>

        <!-- Generator Tab -->
        <div id="generator-tab" class="tab-content">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-8 border border-white/20">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mr-4">
                        <i class="bi bi-gear text-white text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-gray-900">创建代码生成任务</h2>
                        <p class="text-sm text-gray-600">配置项目参数，选择模板，一键生成代码</p>
                    </div>
                </div>

                <form id="generatorForm" class="space-y-8">
                    <!-- 基础配置 -->
                    <div class="bg-gray-50/50 rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="bi bi-gear-fill text-blue-600 mr-2"></i>
                            基础配置
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="input-group">
                                <label class="input-label">
                                    <i class="bi bi-tag text-blue-500 mr-2"></i>
                                    任务名称
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" id="taskName" class="enhanced-input" placeholder="我的代码生成任务" required>
                                    <div class="input-icon">
                                        <i class="bi bi-pencil text-gray-400"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="input-group">
                                <label class="input-label">
                                    <i class="bi bi-folder text-blue-500 mr-2"></i>
                                    项目名称
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" id="projectName" class="enhanced-input" placeholder="my-spring-boot-app" required>
                                    <div class="input-icon">
                                        <i class="bi bi-box text-gray-400"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="input-group">
                                <label class="input-label">
                                    <i class="bi bi-layers text-blue-500 mr-2"></i>
                                    基础包名
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" id="basePackage" class="enhanced-input" placeholder="com.example.demo" required>
                                    <div class="input-icon">
                                        <i class="bi bi-code text-gray-400"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="input-group">
                                <label class="input-label">
                                    <i class="bi bi-folder-symlink text-blue-500 mr-2"></i>
                                    输出路径
                                </label>
                                <div class="input-wrapper">
                                    <input type="text" id="outputPath" class="enhanced-input" placeholder="./generated" required>
                                    <div class="input-icon">
                                        <i class="bi bi-folder-plus text-gray-400"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 模板选择 -->
                    <div class="bg-gray-50/50 rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="bi bi-file-earmark-code text-green-600 mr-2"></i>
                            选择模板
                        </h3>
                        <div id="templateList" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <!-- Spring Boot Basic Template -->
                            <div class="template-card" data-template="spring-boot-basic">
                                <div class="template-header">
                                    <div class="template-icon">
                                        <i class="bi bi-leaf text-green-600"></i>
                                    </div>
                                    <div class="template-badge">推荐</div>
                                </div>
                                <h4 class="template-title">Spring Boot 基础模板</h4>
                                <p class="template-description">包含基础的Controller、Service、Repository层，适合快速开发</p>
                                <div class="template-features">
                                    <span class="feature-tag">Controller</span>
                                    <span class="feature-tag">Service</span>
                                    <span class="feature-tag">Repository</span>
                                    <span class="feature-tag">Entity</span>
                                </div>
                                <div class="template-footer">
                                    <span class="template-version">v2.7.0</span>
                                    <span class="template-downloads">
                                        <i class="bi bi-download text-gray-400 mr-1"></i>1.2k
                                    </span>
                                </div>
                            </div>

                            <!-- Spring Boot REST API Template -->
                            <div class="template-card" data-template="spring-boot-rest">
                                <div class="template-header">
                                    <div class="template-icon">
                                        <i class="bi bi-cloud text-blue-600"></i>
                                    </div>
                                    <div class="template-badge new">新版</div>
                                </div>
                                <h4 class="template-title">REST API 模板</h4>
                                <p class="template-description">完整的RESTful API模板，包含Swagger文档和异常处理</p>
                                <div class="template-features">
                                    <span class="feature-tag">REST API</span>
                                    <span class="feature-tag">Swagger</span>
                                    <span class="feature-tag">Exception</span>
                                    <span class="feature-tag">Validation</span>
                                </div>
                                <div class="template-footer">
                                    <span class="template-version">v3.0.0</span>
                                    <span class="template-downloads">
                                        <i class="bi bi-download text-gray-400 mr-1"></i>856
                                    </span>
                                </div>
                            </div>

                            <!-- Spring Boot Security Template -->
                            <div class="template-card" data-template="spring-boot-security">
                                <div class="template-header">
                                    <div class="template-icon">
                                        <i class="bi bi-shield-check text-purple-600"></i>
                                    </div>
                                    <div class="template-badge">安全</div>
                                </div>
                                <h4 class="template-title">安全认证模板</h4>
                                <p class="template-description">集成Spring Security，包含JWT认证和权限管理</p>
                                <div class="template-features">
                                    <span class="feature-tag">Security</span>
                                    <span class="feature-tag">JWT</span>
                                    <span class="feature-tag">RBAC</span>
                                    <span class="feature-tag">OAuth2</span>
                                </div>
                                <div class="template-footer">
                                    <span class="template-version">v2.5.1</span>
                                    <span class="template-downloads">
                                        <i class="bi bi-download text-gray-400 mr-1"></i>634
                                    </span>
                                </div>
                            </div>

                            <!-- Microservice Template -->
                            <div class="template-card" data-template="microservice">
                                <div class="template-header">
                                    <div class="template-icon">
                                        <i class="bi bi-diagram-3 text-orange-600"></i>
                                    </div>
                                    <div class="template-badge">高级</div>
                                </div>
                                <h4 class="template-title">微服务模板</h4>
                                <p class="template-description">微服务架构模板，包含服务发现、配置中心、网关</p>
                                <div class="template-features">
                                    <span class="feature-tag">Eureka</span>
                                    <span class="feature-tag">Gateway</span>
                                    <span class="feature-tag">Config</span>
                                    <span class="feature-tag">Feign</span>
                                </div>
                                <div class="template-footer">
                                    <span class="template-version">v1.8.2</span>
                                    <span class="template-downloads">
                                        <i class="bi bi-download text-gray-400 mr-1"></i>423
                                    </span>
                                </div>
                            </div>

                            <!-- Data JPA Template -->
                            <div class="template-card" data-template="spring-data-jpa">
                                <div class="template-header">
                                    <div class="template-icon">
                                        <i class="bi bi-database text-teal-600"></i>
                                    </div>
                                    <div class="template-badge">数据</div>
                                </div>
                                <h4 class="template-title">数据持久化模板</h4>
                                <p class="template-description">Spring Data JPA模板，包含多数据源和事务管理</p>
                                <div class="template-features">
                                    <span class="feature-tag">JPA</span>
                                    <span class="feature-tag">MySQL</span>
                                    <span class="feature-tag">Redis</span>
                                    <span class="feature-tag">Transaction</span>
                                </div>
                                <div class="template-footer">
                                    <span class="template-version">v2.3.4</span>
                                    <span class="template-downloads">
                                        <i class="bi bi-download text-gray-400 mr-1"></i>789
                                    </span>
                                </div>
                            </div>

                            <!-- Custom Template -->
                            <div class="template-card template-custom" data-template="custom">
                                <div class="template-header">
                                    <div class="template-icon">
                                        <i class="bi bi-plus-circle text-gray-600"></i>
                                    </div>
                                </div>
                                <h4 class="template-title">自定义模板</h4>
                                <p class="template-description">创建您自己的代码模板，满足特定需求</p>
                                <div class="template-features">
                                    <span class="feature-tag">自定义</span>
                                    <span class="feature-tag">灵活</span>
                                </div>
                                <div class="template-footer">
                                    <button class="create-template-btn">
                                        <i class="bi bi-plus mr-1"></i>创建模板
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 生成变量 -->
                    <div class="bg-gray-50/50 rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="bi bi-sliders text-purple-600 mr-2"></i>
                            生成变量
                        </h3>
                        <div id="variableInputs" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="input-group">
                                    <label class="input-label">
                                        <i class="bi bi-file-earmark-code text-purple-500 mr-2"></i>
                                        实体类名
                                    </label>
                                    <div class="input-wrapper">
                                        <input type="text" id="className" class="enhanced-input" placeholder="User" required>
                                        <div class="input-icon">
                                            <i class="bi bi-braces text-gray-400"></i>
                                        </div>
                                    </div>
                                    <p class="input-hint">Java类名，首字母大写</p>
                                </div>
                                <div class="input-group">
                                    <label class="input-label">
                                        <i class="bi bi-table text-purple-500 mr-2"></i>
                                        表名
                                    </label>
                                    <div class="input-wrapper">
                                        <input type="text" id="tableName" class="enhanced-input" placeholder="users">
                                        <div class="input-icon">
                                            <i class="bi bi-database text-gray-400"></i>
                                        </div>
                                    </div>
                                    <p class="input-hint">数据库表名，小写</p>
                                </div>
                                <div class="input-group">
                                    <label class="input-label">
                                        <i class="bi bi-person text-purple-500 mr-2"></i>
                                        作者
                                    </label>
                                    <div class="input-wrapper">
                                        <input type="text" id="author" class="enhanced-input" placeholder="Developer">
                                        <div class="input-icon">
                                            <i class="bi bi-person-badge text-gray-400"></i>
                                        </div>
                                    </div>
                                    <p class="input-hint">代码注释中的作者信息</p>
                                </div>
                                <div class="input-group">
                                    <label class="input-label">
                                        <i class="bi bi-signpost text-purple-500 mr-2"></i>
                                        请求映射
                                    </label>
                                    <div class="input-wrapper">
                                        <input type="text" id="requestMapping" class="enhanced-input" placeholder="/api/users">
                                        <div class="input-icon">
                                            <i class="bi bi-link text-gray-400"></i>
                                        </div>
                                    </div>
                                    <p class="input-hint">Controller的基础路径</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                        <div class="text-sm text-gray-500">
                            <i class="bi bi-info-circle mr-1"></i>
                            生成的代码将保存到指定的输出路径
                        </div>
                        <div class="flex space-x-3">
                            <button type="button" onclick="resetForm()" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                                <i class="bi bi-arrow-clockwise mr-2"></i>重置
                            </button>
                            <button type="submit" class="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all transform hover:scale-105">
                                <i class="bi bi-play-fill mr-2"></i>开始生成
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Templates Tab -->
        <div id="templates-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-medium text-gray-900">模板管理</h2>
                    <button onclick="showCreateTemplateModal()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                        <i class="bi bi-plus mr-2"></i>新建模板
                    </button>
                </div>
                
                <div id="templatesTable" class="overflow-x-auto">
                    <!-- Templates table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Tasks Tab -->
        <div id="tasks-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">任务管理</h2>
                
                <div id="tasksTable" class="overflow-x-auto">
                    <!-- Tasks table will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script>
        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            // Load tab content
            if (tabName === 'generator') {
                loadTemplates();
            } else if (tabName === 'templates') {
                loadTemplatesTable();
            } else if (tabName === 'tasks') {
                loadTasksTable();
            }
        }

        // Load templates for generator
        async function loadTemplates() {
            try {
                const response = await fetch('/api/code-generator/templates');
                const templates = await response.json();
                
                const templateList = document.getElementById('templateList');
                templateList.innerHTML = templates.map(template => `
                    <div class="border rounded-lg p-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="template-${template.id}" value="${template.id}" class="template-checkbox mr-3">
                            <div>
                                <h4 class="font-medium">${template.templateName}</h4>
                                <p class="text-sm text-gray-600">${template.description || ''}</p>
                                <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded mt-1">${template.templateType}</span>
                            </div>
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                showToast('加载模板失败: ' + error.message, 'error');
            }
        }

        // Load templates table
        async function loadTemplatesTable() {
            // Implementation for templates table
        }

        // Load tasks table
        async function loadTasksTable() {
            // Implementation for tasks table
        }

        // Form submission
        document.getElementById('generatorForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const selectedTemplates = Array.from(document.querySelectorAll('.template-checkbox:checked'))
                .map(cb => parseInt(cb.value));
            
            if (selectedTemplates.length === 0) {
                showToast('请至少选择一个模板', 'warning');
                return;
            }
            
            const formData = {
                taskName: document.getElementById('taskName').value,
                projectName: document.getElementById('projectName').value,
                basePackage: document.getElementById('basePackage').value,
                outputPath: document.getElementById('outputPath').value,
                templateIds: selectedTemplates,
                variables: {
                    className: document.getElementById('className').value,
                    tableName: document.getElementById('tableName').value || document.getElementById('className').value.toLowerCase(),
                    author: document.getElementById('author').value || 'Developer',
                    requestMapping: document.getElementById('requestMapping').value || '/api/' + document.getElementById('className').value.toLowerCase(),
                    entityName: document.getElementById('className').value,
                    serviceName: document.getElementById('className').value + 'Service'
                }
            };
            
            try {
                const response = await fetch('/api/code-generator/tasks', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                if (response.ok) {
                    showToast('代码生成任务创建成功', 'success');
                    document.getElementById('generatorForm').reset();
                    showTab('tasks');
                } else {
                    showToast('创建任务失败', 'error');
                }
            } catch (error) {
                showToast('创建任务失败: ' + error.message, 'error');
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            showTab('generator');
        });
    </script>

    <style>
        .tab-button {
            @apply flex items-center px-4 py-2 rounded-xl font-medium text-sm text-gray-600 hover:text-gray-900 hover:bg-white/50 transition-all duration-200;
        }

        .tab-button.active {
            @apply bg-white text-blue-600 shadow-md;
        }

        /* Enhanced Input Styles */
        .input-group {
            @apply mb-6;
        }

        .input-label {
            @apply flex items-center text-sm font-semibold text-gray-700 mb-3;
        }

        .input-wrapper {
            @apply relative;
        }

        .enhanced-input {
            @apply w-full pl-4 pr-12 py-4 border-2 border-gray-200 rounded-xl bg-white/50 backdrop-blur-sm text-gray-900 placeholder-gray-400 transition-all duration-300 focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white hover:border-gray-300;
        }

        .enhanced-input:focus {
            @apply shadow-lg transform scale-[1.02];
        }

        .input-icon {
            @apply absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none;
        }

        .input-hint {
            @apply text-xs text-gray-500 mt-2 ml-1 flex items-center;
        }

        .input-hint::before {
            content: "💡";
            @apply mr-1;
        }

        /* Template Card Styles */
        .template-card {
            @apply relative bg-white/80 backdrop-blur-sm border-2 border-gray-200 rounded-2xl p-6 cursor-pointer transition-all duration-300 hover:shadow-xl hover:border-blue-300 hover:transform hover:scale-105 group;
        }

        .template-card.selected {
            @apply border-blue-500 bg-blue-50/80 shadow-xl transform scale-105 ring-4 ring-blue-100;
        }

        .template-card.template-custom {
            @apply border-dashed border-gray-300 hover:border-blue-400 bg-gray-50/50;
        }

        .template-header {
            @apply flex items-start justify-between mb-4;
        }

        .template-icon {
            @apply w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center text-xl group-hover:scale-110 transition-transform duration-300;
        }

        .template-badge {
            @apply px-3 py-1 text-xs font-bold rounded-full text-white;
        }

        .template-badge:not(.new):not(.template-badge[class*="安全"]):not(.template-badge[class*="高级"]):not(.template-badge[class*="数据"]) {
            @apply bg-gradient-to-r from-green-500 to-green-600;
        }

        .template-badge.new {
            @apply bg-gradient-to-r from-blue-500 to-blue-600;
        }

        .template-badge:contains("安全") {
            @apply bg-gradient-to-r from-purple-500 to-purple-600;
        }

        .template-badge:contains("高级") {
            @apply bg-gradient-to-r from-orange-500 to-orange-600;
        }

        .template-badge:contains("数据") {
            @apply bg-gradient-to-r from-teal-500 to-teal-600;
        }

        .template-title {
            @apply text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors;
        }

        .template-description {
            @apply text-sm text-gray-600 mb-4 leading-relaxed;
        }

        .template-features {
            @apply flex flex-wrap gap-2 mb-4;
        }

        .feature-tag {
            @apply px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-lg font-medium hover:bg-blue-100 hover:text-blue-700 transition-colors;
        }

        .template-footer {
            @apply flex items-center justify-between text-xs text-gray-500 pt-4 border-t border-gray-100;
        }

        .template-version {
            @apply font-mono bg-gray-100 px-2 py-1 rounded;
        }

        .template-downloads {
            @apply flex items-center;
        }

        .create-template-btn {
            @apply bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-lg font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-300 transform hover:scale-105;
        }

        .task-card {
            @apply bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:shadow-lg transition-all duration-300;
        }

        .status-badge {
            @apply px-3 py-1 rounded-full text-xs font-medium;
        }

        .status-running {
            @apply bg-blue-100 text-blue-800;
        }

        .status-completed {
            @apply bg-green-100 text-green-800;
        }

        .status-failed {
            @apply bg-red-100 text-red-800;
        }

        .status-pending {
            @apply bg-yellow-100 text-yellow-800;
        }
    </style>
</body>
</html>
