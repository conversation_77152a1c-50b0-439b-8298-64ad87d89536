<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码生成器 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1e293b',
                        accent: '#2563eb',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white/95 backdrop-blur-sm shadow-lg border-b border-white/20 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="/" class="flex items-center space-x-3 text-gray-900 hover:text-blue-600 transition-colors">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="bi bi-arrow-left text-white text-sm"></i>
                        </div>
                        <span class="font-semibold">返回主页</span>
                    </a>
                </div>
                <div class="flex items-center">
                    <div class="text-center">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">代码生成器</h1>
                        <p class="text-xs text-gray-500">Code Generator</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">生成器就绪</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Tabs -->
        <div class="mb-6">
            <nav class="flex space-x-8" aria-label="Tabs">
                <button id="tab-generator" class="tab-button active" onclick="showTab('generator')">
                    <i class="bi bi-code-slash mr-2"></i>代码生成
                </button>
                <button id="tab-templates" class="tab-button" onclick="showTab('templates')">
                    <i class="bi bi-file-earmark-code mr-2"></i>模板管理
                </button>
                <button id="tab-tasks" class="tab-button" onclick="showTab('tasks')">
                    <i class="bi bi-list-task mr-2"></i>任务管理
                </button>
            </nav>
        </div>

        <!-- Generator Tab -->
        <div id="generator-tab" class="tab-content">
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">创建代码生成任务</h2>
                
                <form id="generatorForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">任务名称</label>
                            <input type="text" id="taskName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">项目名称</label>
                            <input type="text" id="projectName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">基础包名</label>
                            <input type="text" id="basePackage" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" placeholder="com.example.demo" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">输出路径</label>
                            <input type="text" id="outputPath" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" placeholder="./generated" required>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择模板</label>
                        <div id="templateList" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <!-- Templates will be loaded here -->
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">生成变量</label>
                        <div id="variableInputs" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">实体类名</label>
                                    <input type="text" id="className" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" placeholder="User" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">表名</label>
                                    <input type="text" id="tableName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" placeholder="users">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">作者</label>
                                    <input type="text" id="author" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" placeholder="Developer">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">请求映射</label>
                                    <input type="text" id="requestMapping" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary" placeholder="/api/users">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="bg-primary text-white px-6 py-2 rounded-md hover:bg-accent focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                            <i class="bi bi-play-fill mr-2"></i>开始生成
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Templates Tab -->
        <div id="templates-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-medium text-gray-900">模板管理</h2>
                    <button onclick="showCreateTemplateModal()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                        <i class="bi bi-plus mr-2"></i>新建模板
                    </button>
                </div>
                
                <div id="templatesTable" class="overflow-x-auto">
                    <!-- Templates table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Tasks Tab -->
        <div id="tasks-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">任务管理</h2>
                
                <div id="tasksTable" class="overflow-x-auto">
                    <!-- Tasks table will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script>
        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            // Load tab content
            if (tabName === 'generator') {
                loadTemplates();
            } else if (tabName === 'templates') {
                loadTemplatesTable();
            } else if (tabName === 'tasks') {
                loadTasksTable();
            }
        }

        // Load templates for generator
        async function loadTemplates() {
            try {
                const response = await fetch('/api/code-generator/templates');
                const templates = await response.json();
                
                const templateList = document.getElementById('templateList');
                templateList.innerHTML = templates.map(template => `
                    <div class="border rounded-lg p-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="template-${template.id}" value="${template.id}" class="template-checkbox mr-3">
                            <div>
                                <h4 class="font-medium">${template.templateName}</h4>
                                <p class="text-sm text-gray-600">${template.description || ''}</p>
                                <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded mt-1">${template.templateType}</span>
                            </div>
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                showToast('加载模板失败: ' + error.message, 'error');
            }
        }

        // Load templates table
        async function loadTemplatesTable() {
            // Implementation for templates table
        }

        // Load tasks table
        async function loadTasksTable() {
            // Implementation for tasks table
        }

        // Form submission
        document.getElementById('generatorForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const selectedTemplates = Array.from(document.querySelectorAll('.template-checkbox:checked'))
                .map(cb => parseInt(cb.value));
            
            if (selectedTemplates.length === 0) {
                showToast('请至少选择一个模板', 'warning');
                return;
            }
            
            const formData = {
                taskName: document.getElementById('taskName').value,
                projectName: document.getElementById('projectName').value,
                basePackage: document.getElementById('basePackage').value,
                outputPath: document.getElementById('outputPath').value,
                templateIds: selectedTemplates,
                variables: {
                    className: document.getElementById('className').value,
                    tableName: document.getElementById('tableName').value || document.getElementById('className').value.toLowerCase(),
                    author: document.getElementById('author').value || 'Developer',
                    requestMapping: document.getElementById('requestMapping').value || '/api/' + document.getElementById('className').value.toLowerCase(),
                    entityName: document.getElementById('className').value,
                    serviceName: document.getElementById('className').value + 'Service'
                }
            };
            
            try {
                const response = await fetch('/api/code-generator/tasks', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                if (response.ok) {
                    showToast('代码生成任务创建成功', 'success');
                    document.getElementById('generatorForm').reset();
                    showTab('tasks');
                } else {
                    showToast('创建任务失败', 'error');
                }
            } catch (error) {
                showToast('创建任务失败: ' + error.message, 'error');
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            showTab('generator');
        });
    </script>

    <style>
        .tab-button {
            @apply py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300;
        }
        
        .tab-button.active {
            @apply border-primary text-primary;
        }
    </style>
</body>
</html>
