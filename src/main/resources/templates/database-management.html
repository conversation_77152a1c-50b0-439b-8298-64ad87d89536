<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1e293b',
                        accent: '#2563eb',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-slate-50 via-green-50 to-emerald-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white/95 backdrop-blur-sm shadow-lg border-b border-white/20 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="/" class="flex items-center space-x-3 text-gray-900 hover:text-green-600 transition-colors">
                        <div class="w-8 h-8 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg flex items-center justify-center">
                            <i class="bi bi-arrow-left text-white text-sm"></i>
                        </div>
                        <span class="font-semibold">返回主页</span>
                    </a>
                </div>
                <div class="flex items-center">
                    <div class="text-center">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">数据库管理</h1>
                        <p class="text-xs text-gray-500">Database Management</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">数据库连接</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                <i class="bi bi-database text-green-600 mr-3"></i>
                多数据库管理平台
            </h2>
            <p class="text-gray-600">统一管理多种数据库连接，在线执行SQL查询，查看数据库结构</p>
        </div>

        <!-- Tabs -->
        <div class="mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg border border-white/20">
                <nav class="flex space-x-2" aria-label="Tabs">
                    <button id="tab-connections" class="tab-button active" onclick="showTab('connections')">
                        <i class="bi bi-database mr-2"></i>数据库连接
                    </button>
                    <button id="tab-query" class="tab-button" onclick="showTab('query')">
                        <i class="bi bi-code-slash mr-2"></i>SQL查询
                    </button>
                    <button id="tab-structure" class="tab-button" onclick="showTab('structure')">
                        <i class="bi bi-diagram-3 mr-2"></i>数据库结构
                    </button>
                </nav>
            </div>
        </div>

        <!-- Connections Tab -->
        <div id="connections-tab" class="tab-content">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900">数据库连接管理</h3>
                    <button onclick="showAddConnectionModal()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                        <i class="bi bi-plus mr-2"></i>添加连接
                    </button>
                </div>

                <div id="connectionsTable" class="overflow-x-auto">
                    <!-- Connections table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Query Tab -->
        <div id="query-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Query Input -->
                <div class="lg:col-span-2">
                    <div class="bg-white shadow rounded-lg p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">SQL查询</h3>
                            <div class="flex space-x-2">
                                <select id="queryConnectionSelect" class="text-sm border-2 border-gray-400 rounded-lg bg-white focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-300 px-3 py-2 hover:border-gray-500">
                                    <option value="">选择数据库连接</option>
                                </select>
                                <button onclick="executeQuery()" id="executeButton" 
                                        class="bg-success text-white px-4 py-2 rounded-md hover:bg-green-600 disabled:opacity-50">
                                    <i class="bi bi-play-fill mr-1"></i>执行
                                </button>
                            </div>
                        </div>
                        
                        <textarea id="sqlEditor" rows="10"
                                  class="enhanced-textarea w-full border-2 border-gray-200 rounded-xl bg-white/50 backdrop-blur-sm font-mono text-sm focus:outline-none focus:border-green-500 focus:ring-4 focus:ring-green-100 focus:bg-white transition-all duration-300 p-4"
                                  placeholder="输入SQL查询语句..."></textarea>
                        
                        <div class="mt-4 flex justify-between items-center">
                            <div class="text-sm text-gray-500">
                                <span>最大行数:</span>
                                <input type="number" id="maxRows" value="1000" min="1" max="10000" 
                                       class="ml-2 w-20 border-gray-300 rounded text-sm">
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="clearQuery()" class="text-gray-500 hover:text-gray-700">
                                    <i class="bi bi-trash"></i> 清空
                                </button>
                                <button onclick="formatQuery()" class="text-gray-500 hover:text-gray-700">
                                    <i class="bi bi-code"></i> 格式化
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Query History -->
                <div class="lg:col-span-1">
                    <div class="bg-white shadow rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">查询历史</h3>
                        <div id="queryHistory" class="space-y-2">
                            <!-- Query history will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Query Results -->
            <div class="mt-6 bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">查询结果</h3>
                    <div id="queryStats" class="text-sm text-gray-500">
                        <!-- Query statistics will be shown here -->
                    </div>
                </div>
                
                <div id="queryResults" class="overflow-x-auto">
                    <div class="text-center text-gray-500 py-8">
                        <i class="bi bi-table text-4xl mb-4"></i>
                        <p>执行SQL查询后结果将显示在这里</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Structure Tab -->
        <div id="structure-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Database Tables -->
                <div class="lg:col-span-1">
                    <div class="bg-white shadow rounded-lg p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">数据库表</h3>
                            <select id="structureConnectionSelect" class="text-sm border-gray-300 rounded-md">
                                <option value="">选择数据库</option>
                            </select>
                        </div>
                        
                        <div id="tablesList" class="space-y-1">
                            <!-- Tables list will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Table Structure -->
                <div class="lg:col-span-3">
                    <div class="bg-white shadow rounded-lg p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 id="tableStructureTitle" class="text-lg font-medium text-gray-900">表结构</h3>
                            <div class="flex space-x-2">
                                <button onclick="generateCreateSQL()" class="text-sm bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">
                                    生成建表SQL
                                </button>
                                <button onclick="generateSelectSQL()" class="text-sm bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">
                                    生成查询SQL
                                </button>
                            </div>
                        </div>
                        
                        <div id="tableStructure" class="overflow-x-auto">
                            <div class="text-center text-gray-500 py-8">
                                <i class="bi bi-diagram-3 text-4xl mb-4"></i>
                                <p>选择表查看结构信息</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Connection Modal -->
    <div id="addConnectionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900">添加数据库连接</h3>
                    <button onclick="closeAddConnectionModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                
                <form id="addConnectionForm" class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="modal-input-group">
                            <label class="modal-input-label">
                                <i class="bi bi-tag text-green-500 mr-2"></i>
                                连接名称
                            </label>
                            <input type="text" id="connectionName" class="modal-enhanced-input" placeholder="我的数据库连接" required>
                        </div>
                        <div class="modal-input-group">
                            <label class="modal-input-label">
                                <i class="bi bi-database text-green-500 mr-2"></i>
                                数据库类型
                            </label>
                            <select id="databaseType" class="modal-enhanced-select" required>
                                <option value="MYSQL">MySQL</option>
                                <option value="POSTGRESQL">PostgreSQL</option>
                                <option value="ORACLE">Oracle</option>
                                <option value="SQL_SERVER">SQL Server</option>
                                <option value="H2">H2</option>
                                <option value="SQLITE">SQLite</option>
                            </select>
                        </div>
                        <div class="modal-input-group">
                            <label class="modal-input-label">
                                <i class="bi bi-hdd text-green-500 mr-2"></i>
                                主机地址
                            </label>
                            <input type="text" id="host" class="modal-enhanced-input" placeholder="localhost" required>
                        </div>
                        <div class="modal-input-group">
                            <label class="modal-input-label">
                                <i class="bi bi-ethernet text-green-500 mr-2"></i>
                                端口
                            </label>
                            <input type="number" id="port" class="modal-enhanced-input" placeholder="3306" required>
                        </div>
                        <div class="modal-input-group">
                            <label class="modal-input-label">
                                <i class="bi bi-collection text-green-500 mr-2"></i>
                                数据库名
                            </label>
                            <input type="text" id="databaseName" class="modal-enhanced-input" placeholder="mydb" required>
                        </div>
                        <div class="modal-input-group">
                            <label class="modal-input-label">
                                <i class="bi bi-person text-green-500 mr-2"></i>
                                用户名
                            </label>
                            <input type="text" id="username" class="modal-enhanced-input" placeholder="root" required>
                        </div>
                        <div class="modal-input-group md:col-span-2">
                            <label class="modal-input-label">
                                <i class="bi bi-key text-green-500 mr-2"></i>
                                密码
                            </label>
                            <input type="password" id="password" class="modal-enhanced-input" placeholder="••••••••" required>
                        </div>
                        <div class="modal-input-group md:col-span-2">
                            <label class="modal-input-label">
                                <i class="bi bi-chat-text text-green-500 mr-2"></i>
                                描述
                            </label>
                            <textarea id="description" rows="3" class="modal-enhanced-textarea" placeholder="数据库连接描述..."></textarea>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" id="isDefault" class="rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">设为默认连接</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddConnectionModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            取消
                        </button>
                        <button type="button" onclick="testConnection()" class="bg-warning text-white px-4 py-2 rounded-md hover:bg-yellow-600">
                            测试连接
                        </button>
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            添加
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script>
        let connections = [];
        let currentConnection = null;
        let queryHistory = [];

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            // Load tab content
            if (tabName === 'connections') {
                loadConnections();
            } else if (tabName === 'query') {
                loadConnectionsForQuery();
            } else if (tabName === 'structure') {
                loadConnectionsForStructure();
            }
        }

        // Load connections
        function loadConnections() {
            const connectionsContainer = document.getElementById('connectionsTable');
            // Mock data - replace with actual API call
            const mockConnections = [
                {
                    id: 1,
                    name: 'MySQL主库',
                    type: 'MYSQL',
                    host: 'localhost',
                    port: 3306,
                    database: 'myapp',
                    status: 'connected'
                },
                {
                    id: 2,
                    name: 'PostgreSQL测试库',
                    type: 'POSTGRESQL',
                    host: '*************',
                    port: 5432,
                    database: 'testdb',
                    status: 'disconnected'
                }
            ];

            connectionsContainer.innerHTML = `
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">连接名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地址</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        ${mockConnections.map(conn => `
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${conn.name}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${conn.type}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${conn.host}:${conn.port}/${conn.database}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${conn.status === 'connected' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                        ${conn.status === 'connected' ? '已连接' : '未连接'}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button class="text-green-600 hover:text-green-900">测试</button>
                                    <button class="text-red-600 hover:text-red-900">删除</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }

        // Show add connection modal
        function showAddConnectionModal() {
            document.getElementById('addConnectionModal').classList.remove('hidden');
        }

        // Close add connection modal
        function closeAddConnectionModal() {
            document.getElementById('addConnectionModal').classList.add('hidden');
            document.getElementById('addConnectionForm').reset();
        }

        // Test connection
        function testConnection() {
            const formData = {
                type: document.getElementById('databaseType').value,
                host: document.getElementById('host').value,
                port: document.getElementById('port').value,
                database: document.getElementById('databaseName').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value
            };

            console.log('Testing connection:', formData);
            showToast('连接测试成功！', 'success');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            showTab('connections');

            // Handle add connection form
            document.getElementById('addConnectionForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = {
                    name: document.getElementById('connectionName').value,
                    type: document.getElementById('databaseType').value,
                    host: document.getElementById('host').value,
                    port: document.getElementById('port').value,
                    database: document.getElementById('databaseName').value,
                    username: document.getElementById('username').value,
                    password: document.getElementById('password').value,
                    description: document.getElementById('description').value,
                    isDefault: document.getElementById('isDefault').checked
                };

                console.log('Adding connection:', formData);
                showToast('数据库连接添加成功！', 'success');
                closeAddConnectionModal();
                loadConnections();
            });
        });
    </script>

    <style>
        .tab-button {
            @apply flex items-center px-4 py-2 rounded-xl font-medium text-sm text-gray-600 hover:text-gray-900 hover:bg-white/50 transition-all duration-200;
        }

        .tab-button.active {
            @apply bg-white text-green-600 shadow-md;
        }

        .table-item {
            @apply p-3 text-sm cursor-pointer hover:bg-green-50 rounded-lg transition-colors duration-200 border border-transparent;
        }

        .table-item:hover {
            @apply border-green-200;
        }

        .table-item.selected {
            @apply bg-green-100 text-green-800 border-green-300;
        }

        .connection-card {
            @apply bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:shadow-lg transition-all duration-300;
        }

        .query-editor {
            @apply bg-gray-50 border-2 border-gray-200 rounded-lg p-4 font-mono text-sm focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200;
        }

        .sql-result-table {
            @apply min-w-full divide-y divide-gray-200 bg-white rounded-lg overflow-hidden shadow;
        }

        .sql-result-header {
            @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
        }

        .sql-result-cell {
            @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
        }

        /* Enhanced Input Styles for Database Management */
        .enhanced-textarea {
            @apply w-full border-2 border-gray-400 rounded-lg bg-white font-mono text-sm focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-300 p-4 hover:border-gray-500;
        }

        .modal-input-group {
            @apply mb-4;
        }

        .modal-input-label {
            @apply flex items-center text-sm font-semibold text-gray-700 mb-2;
        }

        .modal-enhanced-input {
            @apply w-full px-4 py-3 border-2 border-gray-400 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-300 hover:border-gray-500;
        }

        .modal-enhanced-select {
            @apply w-full px-4 py-3 border-2 border-gray-400 rounded-lg bg-white text-gray-900 focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-300 hover:border-gray-500;
        }

        .modal-enhanced-textarea {
            @apply w-full px-4 py-3 border-2 border-gray-400 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-300 hover:border-gray-500 resize-none;
        }
    </style>
</body>
</html>
