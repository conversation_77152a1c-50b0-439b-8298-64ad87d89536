<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3498db',
                        secondary: '#2c3e50',
                        accent: '#2980b9',
                        success: '#2ecc71',
                        warning: '#f1c40f',
                        danger: '#e74c3c'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-xl font-semibold text-gray-900">
                        <i class="bi bi-arrow-left mr-2"></i>返回主页
                    </a>
                </div>
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">数据库管理</h1>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Tabs -->
        <div class="mb-6">
            <nav class="flex space-x-8" aria-label="Tabs">
                <button id="tab-connections" class="tab-button active" onclick="showTab('connections')">
                    <i class="bi bi-database mr-2"></i>数据库连接
                </button>
                <button id="tab-query" class="tab-button" onclick="showTab('query')">
                    <i class="bi bi-code-slash mr-2"></i>SQL查询
                </button>
                <button id="tab-structure" class="tab-button" onclick="showTab('structure')">
                    <i class="bi bi-diagram-3 mr-2"></i>数据库结构
                </button>
            </nav>
        </div>

        <!-- Connections Tab -->
        <div id="connections-tab" class="tab-content">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900">数据库连接管理</h3>
                    <button onclick="showAddConnectionModal()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                        <i class="bi bi-plus mr-2"></i>添加连接
                    </button>
                </div>

                <div id="connectionsTable" class="overflow-x-auto">
                    <!-- Connections table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Query Tab -->
        <div id="query-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Query Input -->
                <div class="lg:col-span-2">
                    <div class="bg-white shadow rounded-lg p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">SQL查询</h3>
                            <div class="flex space-x-2">
                                <select id="queryConnectionSelect" class="text-sm border-gray-300 rounded-md">
                                    <option value="">选择数据库连接</option>
                                </select>
                                <button onclick="executeQuery()" id="executeButton" 
                                        class="bg-success text-white px-4 py-2 rounded-md hover:bg-green-600 disabled:opacity-50">
                                    <i class="bi bi-play-fill mr-1"></i>执行
                                </button>
                            </div>
                        </div>
                        
                        <textarea id="sqlEditor" rows="10" 
                                  class="w-full border-gray-300 rounded-md focus:ring-primary focus:border-primary font-mono text-sm"
                                  placeholder="输入SQL查询语句..."></textarea>
                        
                        <div class="mt-4 flex justify-between items-center">
                            <div class="text-sm text-gray-500">
                                <span>最大行数:</span>
                                <input type="number" id="maxRows" value="1000" min="1" max="10000" 
                                       class="ml-2 w-20 border-gray-300 rounded text-sm">
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="clearQuery()" class="text-gray-500 hover:text-gray-700">
                                    <i class="bi bi-trash"></i> 清空
                                </button>
                                <button onclick="formatQuery()" class="text-gray-500 hover:text-gray-700">
                                    <i class="bi bi-code"></i> 格式化
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Query History -->
                <div class="lg:col-span-1">
                    <div class="bg-white shadow rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">查询历史</h3>
                        <div id="queryHistory" class="space-y-2">
                            <!-- Query history will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Query Results -->
            <div class="mt-6 bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">查询结果</h3>
                    <div id="queryStats" class="text-sm text-gray-500">
                        <!-- Query statistics will be shown here -->
                    </div>
                </div>
                
                <div id="queryResults" class="overflow-x-auto">
                    <div class="text-center text-gray-500 py-8">
                        <i class="bi bi-table text-4xl mb-4"></i>
                        <p>执行SQL查询后结果将显示在这里</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Structure Tab -->
        <div id="structure-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Database Tables -->
                <div class="lg:col-span-1">
                    <div class="bg-white shadow rounded-lg p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">数据库表</h3>
                            <select id="structureConnectionSelect" class="text-sm border-gray-300 rounded-md">
                                <option value="">选择数据库</option>
                            </select>
                        </div>
                        
                        <div id="tablesList" class="space-y-1">
                            <!-- Tables list will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Table Structure -->
                <div class="lg:col-span-3">
                    <div class="bg-white shadow rounded-lg p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 id="tableStructureTitle" class="text-lg font-medium text-gray-900">表结构</h3>
                            <div class="flex space-x-2">
                                <button onclick="generateCreateSQL()" class="text-sm bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">
                                    生成建表SQL
                                </button>
                                <button onclick="generateSelectSQL()" class="text-sm bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">
                                    生成查询SQL
                                </button>
                            </div>
                        </div>
                        
                        <div id="tableStructure" class="overflow-x-auto">
                            <div class="text-center text-gray-500 py-8">
                                <i class="bi bi-diagram-3 text-4xl mb-4"></i>
                                <p>选择表查看结构信息</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Connection Modal -->
    <div id="addConnectionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900">添加数据库连接</h3>
                    <button onclick="closeAddConnectionModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                
                <form id="addConnectionForm" class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">连接名称</label>
                            <input type="text" id="connectionName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">数据库类型</label>
                            <select id="databaseType" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                                <option value="MYSQL">MySQL</option>
                                <option value="POSTGRESQL">PostgreSQL</option>
                                <option value="ORACLE">Oracle</option>
                                <option value="SQL_SERVER">SQL Server</option>
                                <option value="H2">H2</option>
                                <option value="SQLITE">SQLite</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">主机地址</label>
                            <input type="text" id="host" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">端口</label>
                            <input type="number" id="port" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">数据库名</label>
                            <input type="text" id="databaseName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">用户名</label>
                            <input type="text" id="username" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">密码</label>
                            <input type="password" id="password" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">描述</label>
                            <textarea id="description" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" id="isDefault" class="rounded border-gray-300 text-primary shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">设为默认连接</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddConnectionModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            取消
                        </button>
                        <button type="button" onclick="testConnection()" class="bg-warning text-white px-4 py-2 rounded-md hover:bg-yellow-600">
                            测试连接
                        </button>
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            添加
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script>
        let connections = [];
        let currentConnection = null;
        let queryHistory = [];

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            // Load tab content
            if (tabName === 'connections') {
                loadConnections();
            } else if (tabName === 'query') {
                loadConnectionsForQuery();
            } else if (tabName === 'structure') {
                loadConnectionsForStructure();
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            showTab('connections');
        });
    </script>

    <style>
        .tab-button {
            @apply py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300;
        }
        
        .tab-button.active {
            @apply border-primary text-primary;
        }

        .table-item {
            @apply p-2 text-sm cursor-pointer hover:bg-gray-100 rounded;
        }

        .table-item.selected {
            @apply bg-blue-100 text-blue-800;
        }
    </style>
</body>
</html>
