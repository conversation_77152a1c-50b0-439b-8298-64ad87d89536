<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志管理 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1e293b',
                        accent: '#2563eb',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white/95 backdrop-blur-sm shadow-lg border-b border-white/20 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="/" class="flex items-center space-x-3 text-gray-900 hover:text-blue-600 transition-colors">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="bi bi-arrow-left text-white text-sm"></i>
                        </div>
                        <span class="font-semibold">返回主页</span>
                    </a>
                </div>
                <div class="flex items-center">
                    <div class="text-center">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">日志管理</h1>
                        <p class="text-xs text-gray-500">Log Management System</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="refreshLogs()" class="p-2 rounded-lg bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors">
                        <i class="bi bi-arrow-clockwise text-lg"></i>
                    </button>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">实时监控</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Statistics Cards -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="bi bi-graph-up text-blue-600 mr-3"></i>
                日志统计概览
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Total Logs -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 border border-white/20 hover:shadow-xl transition-all duration-300">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 mb-1">总日志数</p>
                            <p id="totalLogs" class="text-3xl font-bold text-gray-900">--</p>
                            <p class="text-xs text-gray-500 mt-1">系统总计</p>
                        </div>
                        <div class="w-14 h-14 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <i class="bi bi-file-text text-white text-xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Error Logs -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 border border-white/20 hover:shadow-xl transition-all duration-300">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 mb-1">错误日志</p>
                            <p id="errorLogs" class="text-3xl font-bold text-red-600">--</p>
                            <p class="text-xs text-gray-500 mt-1">需要关注</p>
                        </div>
                        <div class="w-14 h-14 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <i class="bi bi-exclamation-triangle text-white text-xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Today Logs -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 border border-white/20 hover:shadow-xl transition-all duration-300">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 mb-1">今日日志</p>
                            <p id="todayLogs" class="text-3xl font-bold text-green-600">--</p>
                            <p class="text-xs text-gray-500 mt-1">24小时内</p>
                        </div>
                        <div class="w-14 h-14 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <i class="bi bi-calendar-day text-white text-xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Operations -->
                <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 border border-white/20 hover:shadow-xl transition-all duration-300">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 mb-1">操作记录</p>
                            <p id="operationLogs" class="text-3xl font-bold text-purple-600">--</p>
                            <p class="text-xs text-gray-500 mt-1">用户操作</p>
                        </div>
                        <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <i class="bi bi-gear text-white text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg border border-white/20">
                <nav class="flex space-x-2" aria-label="Tabs">
                    <button id="tab-system" class="tab-button active" onclick="showTab('system')">
                        <i class="bi bi-cpu mr-2"></i>系统日志
                    </button>
                    <button id="tab-operations" class="tab-button" onclick="showTab('operations')">
                        <i class="bi bi-person-gear mr-2"></i>操作日志
                    </button>
                    <button id="tab-errors" class="tab-button" onclick="showTab('errors')">
                        <i class="bi bi-bug mr-2"></i>错误日志
                    </button>
                    <button id="tab-analysis" class="tab-button" onclick="showTab('analysis')">
                        <i class="bi bi-graph-up mr-2"></i>日志分析
                    </button>
                </nav>
            </div>
        </div>

        <!-- System Logs Tab -->
        <div id="system-tab" class="tab-content">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900">系统日志</h3>
                    <div class="flex space-x-3">
                        <select id="logLevelFilter" class="text-sm border-gray-300 rounded-md">
                            <option value="">所有级别</option>
                            <option value="ERROR">ERROR</option>
                            <option value="WARN">WARN</option>
                            <option value="INFO">INFO</option>
                            <option value="DEBUG">DEBUG</option>
                        </select>
                        <input type="text" id="logSearchInput" placeholder="搜索日志..." 
                               class="text-sm border-gray-300 rounded-md">
                        <button onclick="searchLogs()" class="bg-primary text-white px-3 py-1 rounded text-sm hover:bg-accent">
                            搜索
                        </button>
                        <button onclick="exportLogs('system')" class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">
                            导出
                        </button>
                    </div>
                </div>

                <div id="systemLogsTable" class="overflow-x-auto">
                    <!-- System logs table will be loaded here -->
                    <div class="text-center py-8 text-gray-500">
                        <i class="bi bi-hourglass-split text-3xl mb-2"></i>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Operations Logs Tab -->
        <div id="operations-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900">操作日志</h3>
                    <div class="flex space-x-3">
                        <select id="operationTypeFilter" class="text-sm border-gray-300 rounded-md">
                            <option value="">所有操作</option>
                            <option value="CREATE">新增</option>
                            <option value="UPDATE">修改</option>
                            <option value="DELETE">删除</option>
                            <option value="LOGIN">登录</option>
                            <option value="LOGOUT">登出</option>
                        </select>
                        <select id="operationStatusFilter" class="text-sm border-gray-300 rounded-md">
                            <option value="">所有状态</option>
                            <option value="SUCCESS">成功</option>
                            <option value="FAILURE">失败</option>
                        </select>
                        <button onclick="exportLogs('operations')" class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">
                            导出
                        </button>
                    </div>
                </div>

                <div id="operationLogsTable" class="overflow-x-auto">
                    <!-- Operation logs table will be loaded here -->
                    <div class="text-center py-8 text-gray-500">
                        <i class="bi bi-hourglass-split text-3xl mb-2"></i>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Logs Tab -->
        <div id="errors-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900">错误日志</h3>
                    <div class="flex space-x-3">
                        <button onclick="clearErrorLogs()" class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                            清理错误日志
                        </button>
                        <button onclick="exportLogs('errors')" class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">
                            导出
                        </button>
                    </div>
                </div>

                <div id="errorLogsTable" class="overflow-x-auto">
                    <!-- Error logs table will be loaded here -->
                    <div class="text-center py-8 text-gray-500">
                        <i class="bi bi-hourglass-split text-3xl mb-2"></i>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analysis Tab -->
        <div id="analysis-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Log Level Distribution -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">日志级别分布</h3>
                    <canvas id="logLevelChart" width="400" height="200"></canvas>
                </div>

                <!-- Hourly Log Count -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">24小时日志趋势</h3>
                    <canvas id="hourlyLogChart" width="400" height="200"></canvas>
                </div>

                <!-- Top Errors -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">频繁错误</h3>
                    <div id="topErrorsList">
                        <!-- Top errors list will be loaded here -->
                    </div>
                </div>

                <!-- Active Users -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">活跃用户</h3>
                    <div id="activeUsersList">
                        <!-- Active users list will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script>
        let logLevelChart, hourlyLogChart;

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            // Load tab content
            if (tabName === 'system') {
                loadSystemLogs();
            } else if (tabName === 'operations') {
                loadOperationLogs();
            } else if (tabName === 'errors') {
                loadErrorLogs();
            } else if (tabName === 'analysis') {
                loadLogAnalysis();
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            loadLogStatistics();
            showTab('system');
        });

        // Load log statistics
        function loadLogStatistics() {
            // Mock data - replace with actual API calls
            document.getElementById('totalLogs').textContent = '12,345';
            document.getElementById('errorLogs').textContent = '23';
            document.getElementById('todayLogs').textContent = '1,234';
            document.getElementById('operationLogs').textContent = '567';
        }

        // Load system logs
        function loadSystemLogs() {
            const container = document.getElementById('systemLogsTable');
            container.innerHTML = `
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">级别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Logger</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">消息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-15 10:30:25</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">ERROR</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">com.appadmin.service.UserService</td>
                            <td class="px-6 py-4 text-sm text-gray-900">用户登录失败: 密码错误</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-indigo-600 hover:text-indigo-900">查看详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-15 10:29:15</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">INFO</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">com.appadmin.controller.ProjectController</td>
                            <td class="px-6 py-4 text-sm text-gray-900">项目创建成功: SpringBoot Demo</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-indigo-600 hover:text-indigo-900">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            `;
        }

        // Load operation logs
        function loadOperationLogs() {
            const container = document.getElementById('operationLogsTable');
            container.innerHTML = `
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模块</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-15 10:30:25</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">admin</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">创建项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目管理</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">成功</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-indigo-600 hover:text-indigo-900">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            `;
        }

        // Load error logs
        function loadErrorLogs() {
            const container = document.getElementById('errorLogsTable');
            container.innerHTML = `
                <div class="space-y-4">
                    <div class="border-l-4 border-red-400 bg-red-50 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="bi bi-exclamation-triangle text-red-400"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">数据库连接失败</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <p>java.sql.SQLException: Connection refused</p>
                                    <p class="mt-1 text-xs text-red-600">2024-01-15 10:30:25 - com.appadmin.service.DatabaseService</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Load log analysis
        function loadLogAnalysis() {
            initializeCharts();
        }

        // Initialize charts
        function initializeCharts() {
            // Log Level Chart
            const logLevelCtx = document.getElementById('logLevelChart').getContext('2d');
            logLevelChart = new Chart(logLevelCtx, {
                type: 'doughnut',
                data: {
                    labels: ['INFO', 'WARN', 'ERROR', 'DEBUG'],
                    datasets: [{
                        data: [65, 20, 10, 5],
                        backgroundColor: ['#28a745', '#ffc107', '#dc3545', '#17a2b8']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Hourly Log Chart
            const hourlyLogCtx = document.getElementById('hourlyLogChart').getContext('2d');
            hourlyLogChart = new Chart(hourlyLogCtx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 24}, (_, i) => i + ':00'),
                    datasets: [{
                        label: '日志数量',
                        data: [12, 19, 3, 5, 2, 3, 20, 25, 30, 35, 40, 45, 50, 48, 42, 38, 35, 30, 25, 20, 15, 12, 8, 5],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Search logs
        function searchLogs() {
            const keyword = document.getElementById('logSearchInput').value;
            const level = document.getElementById('logLevelFilter').value;
            
            // Implement search logic
            showToast('搜索功能开发中...', 'info');
        }

        // Export logs
        function exportLogs(type) {
            showToast(`导出${type}日志功能开发中...`, 'info');
        }

        // Clear error logs
        function clearErrorLogs() {
            if (confirm('确定要清理错误日志吗？')) {
                showToast('清理错误日志功能开发中...', 'info');
            }
        }

        // Refresh logs
        function refreshLogs() {
            loadLogStatistics();
            const activeTab = document.querySelector('.tab-button.active').id.replace('tab-', '');
            showTab(activeTab);
            showToast('日志已刷新', 'success');
        }
    </script>

    <style>
        .tab-button {
            @apply flex items-center px-4 py-2 rounded-xl font-medium text-sm text-gray-600 hover:text-gray-900 hover:bg-white/50 transition-all duration-200;
        }

        .tab-button.active {
            @apply bg-white text-blue-600 shadow-md;
        }
    </style>
</body>
</html>
