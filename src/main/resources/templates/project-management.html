<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理 - Spring Boot Application Manager</title>
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.13.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1e293b',
                        accent: '#2563eb',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-slate-50 via-purple-50 to-indigo-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white/95 backdrop-blur-sm shadow-lg border-b border-white/20 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="/" class="flex items-center space-x-3 text-gray-900 hover:text-purple-600 transition-colors">
                        <div class="w-8 h-8 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg flex items-center justify-center">
                            <i class="bi bi-arrow-left text-white text-sm"></i>
                        </div>
                        <span class="font-semibold">返回主页</span>
                    </a>
                </div>
                <div class="flex items-center">
                    <div class="text-center">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">项目管理</h1>
                        <p class="text-xs text-gray-500">Project Management</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-sm text-gray-600">项目同步</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                <i class="bi bi-folder text-purple-600 mr-3"></i>
                项目生命周期管理平台
            </h2>
            <p class="text-gray-600">统一管理项目开发、部署、监控的全生命周期，支持团队协作和环境管理</p>
        </div>

        <!-- Tabs -->
        <div class="mb-8">
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg border border-white/20">
                <nav class="flex space-x-2" aria-label="Tabs">
                    <button id="tab-projects" class="tab-button active" onclick="showTab('projects')">
                        <i class="bi bi-folder mr-2"></i>项目列表
                    </button>
                    <button id="tab-members" class="tab-button" onclick="showTab('members')">
                        <i class="bi bi-people mr-2"></i>成员管理
                    </button>
                    <button id="tab-environments" class="tab-button" onclick="showTab('environments')">
                        <i class="bi bi-server mr-2"></i>环境管理
                    </button>
                    <button id="tab-statistics" class="tab-button" onclick="showTab('statistics')">
                        <i class="bi bi-graph-up mr-2"></i>统计信息
                    </button>
                </nav>
            </div>
        </div>

        <!-- Projects Tab -->
        <div id="projects-tab" class="tab-content">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900">项目列表</h3>
                    <div class="flex space-x-3">
                        <div class="relative">
                            <input type="text" id="projectSearch" placeholder="搜索项目..." 
                                   class="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary">
                            <i class="bi bi-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <button onclick="showCreateProjectModal()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            <i class="bi bi-plus mr-2"></i>创建项目
                        </button>
                    </div>
                </div>

                <div id="projectsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Projects will be loaded here -->
                </div>

                <!-- Pagination -->
                <div id="projectsPagination" class="mt-6 flex justify-center">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Members Tab -->
        <div id="members-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">成员管理</h3>
                        <div class="mt-2">
                            <select id="memberProjectSelect" class="border-gray-300 rounded-md">
                                <option value="">选择项目</option>
                            </select>
                        </div>
                    </div>
                    <button onclick="showAddMemberModal()" id="addMemberBtn" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent" disabled>
                        <i class="bi bi-person-plus mr-2"></i>添加成员
                    </button>
                </div>

                <div id="membersTable" class="overflow-x-auto">
                    <!-- Members table will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Environments Tab -->
        <div id="environments-tab" class="tab-content hidden">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">环境管理</h3>
                        <div class="mt-2">
                            <select id="envProjectSelect" class="border-gray-300 rounded-md">
                                <option value="">选择项目</option>
                            </select>
                        </div>
                    </div>
                    <button onclick="showCreateEnvironmentModal()" id="createEnvBtn" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent" disabled>
                        <i class="bi bi-plus mr-2"></i>创建环境
                    </button>
                </div>

                <div id="environmentsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Environments will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Statistics Tab -->
        <div id="statistics-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Statistics cards will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Create Project Modal -->
    <div id="createProjectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-screen overflow-y-auto">
                <div class="px-6 py-4 border-b">
                    <h3 class="text-lg font-medium text-gray-900">创建项目</h3>
                    <button onclick="closeCreateProjectModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                
                <form id="createProjectForm" class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">项目代码</label>
                            <input type="text" id="projectCode" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                            <p class="mt-1 text-sm text-gray-500">项目的唯一标识符</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">项目名称</label>
                            <input type="text" id="projectName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">项目类型</label>
                            <select id="projectType" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                                <option value="WEB_APPLICATION">Web应用</option>
                                <option value="MOBILE_APPLICATION">移动应用</option>
                                <option value="DESKTOP_APPLICATION">桌面应用</option>
                                <option value="API_SERVICE">API服务</option>
                                <option value="MICROSERVICE">微服务</option>
                                <option value="LIBRARY">库/组件</option>
                                <option value="FRAMEWORK">框架</option>
                                <option value="TOOL">工具</option>
                                <option value="OTHER">其他</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">项目负责人</label>
                            <input type="text" id="owner" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">项目描述</label>
                            <textarea id="projectDescription" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">仓库地址</label>
                            <input type="url" id="repositoryUrl" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">仓库类型</label>
                            <select id="repositoryType" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                                <option value="">选择类型</option>
                                <option value="git">Git</option>
                                <option value="svn">SVN</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">分支</label>
                            <input type="text" id="repositoryBranch" value="main" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">构建工具</label>
                            <select id="buildTool" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                                <option value="">选择构建工具</option>
                                <option value="maven">Maven</option>
                                <option value="gradle">Gradle</option>
                                <option value="npm">NPM</option>
                                <option value="yarn">Yarn</option>
                                <option value="webpack">Webpack</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">开始日期</label>
                            <input type="date" id="startDate" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">结束日期</label>
                            <input type="date" id="endDate" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">标签</label>
                            <input type="text" id="tags" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" 
                                   placeholder="用逗号分隔多个标签">
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeCreateProjectModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            取消
                        </button>
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-accent">
                            创建项目
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Project Detail Modal -->
    <div id="projectDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="px-6 py-4 border-b">
                    <h3 id="projectDetailTitle" class="text-lg font-medium text-gray-900">项目详情</h3>
                    <button onclick="closeProjectDetailModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                
                <div id="projectDetailContent" class="p-6">
                    <!-- Project details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <!-- Scripts -->
    <script src="/js/utils.js"></script>
    <script src="/js/toast.js"></script>
    <script>
        let projects = [];
        let currentProject = null;
        let currentPage = 0;
        const pageSize = 9;

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            // Load tab content
            if (tabName === 'projects') {
                loadProjects();
            } else if (tabName === 'members') {
                loadProjectsForMembers();
            } else if (tabName === 'environments') {
                loadProjectsForEnvironments();
            } else if (tabName === 'statistics') {
                loadStatistics();
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            showTab('projects');
            
            // Search functionality
            document.getElementById('projectSearch').addEventListener('input', (e) => {
                const keyword = e.target.value.trim();
                if (keyword) {
                    searchProjects(keyword);
                } else {
                    loadProjects();
                }
            });
        });
    </script>

    <style>
        .tab-button {
            @apply flex items-center px-4 py-2 rounded-xl font-medium text-sm text-gray-600 hover:text-gray-900 hover:bg-white/50 transition-all duration-200;
        }

        .tab-button.active {
            @apply bg-white text-purple-600 shadow-md;
        }

        .project-card {
            @apply bg-white/80 backdrop-blur-sm border border-white/20 rounded-xl p-6 hover:shadow-lg transition-all duration-300 cursor-pointer;
        }

        .project-card:hover {
            @apply border-purple-300 transform scale-105;
        }

        .status-badge {
            @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
        }

        .status-planning { @apply bg-gray-100 text-gray-800; }
        .status-development { @apply bg-blue-100 text-blue-800; }
        .status-testing { @apply bg-yellow-100 text-yellow-800; }
        .status-staging { @apply bg-purple-100 text-purple-800; }
        .status-production { @apply bg-green-100 text-green-800; }
        .status-maintenance { @apply bg-orange-100 text-orange-800; }
        .status-archived { @apply bg-gray-100 text-gray-800; }
        .status-cancelled { @apply bg-red-100 text-red-800; }

        .member-card {
            @apply bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:shadow-lg transition-all duration-300;
        }

        .environment-card {
            @apply bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:shadow-lg transition-all duration-300;
        }

        .stats-card {
            @apply bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:shadow-lg transition-all duration-300;
        }
    </style>
</body>
</html>
