package com.appadmin.repository;

import com.appadmin.entity.SystemLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统日志仓储接口
 */
@Repository
public interface SystemLogRepository extends JpaRepository<SystemLog, Long> {

    /**
     * 根据日志级别查找日志
     */
    Page<SystemLog> findByLogLevelOrderByLogTimeDesc(SystemLog.LogLevel logLevel, Pageable pageable);

    /**
     * 根据日志级别列表查找日志
     */
    Page<SystemLog> findByLogLevelInOrderByLogTimeDesc(List<SystemLog.LogLevel> logLevels, Pageable pageable);

    /**
     * 根据时间范围查找日志
     */
    Page<SystemLog> findByLogTimeBetweenOrderByLogTimeDesc(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 根据日志来源查找日志
     */
    Page<SystemLog> findByLogSourceOrderByLogTimeDesc(SystemLog.LogSource logSource, Pageable pageable);

    /**
     * 根据Logger名称查找日志
     */
    Page<SystemLog> findByLoggerNameContainingOrderByLogTimeDesc(String loggerName, Pageable pageable);

    /**
     * 根据线程名称查找日志
     */
    Page<SystemLog> findByThreadNameOrderByLogTimeDesc(String threadName, Pageable pageable);

    /**
     * 根据用户ID查找日志
     */
    Page<SystemLog> findByUserIdOrderByLogTimeDesc(String userId, Pageable pageable);

    /**
     * 根据会话ID查找日志
     */
    Page<SystemLog> findBySessionIdOrderByLogTimeDesc(String sessionId, Pageable pageable);

    /**
     * 根据请求ID查找日志
     */
    Page<SystemLog> findByRequestIdOrderByLogTimeDesc(String requestId, Pageable pageable);

    /**
     * 搜索日志消息
     */
    @Query("SELECT l FROM SystemLog l WHERE LOWER(l.message) LIKE LOWER(CONCAT('%', :keyword, '%')) ORDER BY l.logTime DESC")
    Page<SystemLog> searchByMessage(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 复合搜索日志
     */
    @Query("SELECT l FROM SystemLog l WHERE " +
           "(:logLevel IS NULL OR l.logLevel = :logLevel) AND " +
           "(:logSource IS NULL OR l.logSource = :logSource) AND " +
           "(:startTime IS NULL OR l.logTime >= :startTime) AND " +
           "(:endTime IS NULL OR l.logTime <= :endTime) AND " +
           "(:keyword IS NULL OR LOWER(l.message) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           " LOWER(l.loggerName) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY l.logTime DESC")
    Page<SystemLog> searchLogs(@Param("logLevel") SystemLog.LogLevel logLevel,
                              @Param("logSource") SystemLog.LogSource logSource,
                              @Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime,
                              @Param("keyword") String keyword,
                              Pageable pageable);

    /**
     * 查找错误日志
     */
    @Query("SELECT l FROM SystemLog l WHERE l.logLevel IN ('ERROR', 'FATAL') ORDER BY l.logTime DESC")
    Page<SystemLog> findErrorLogs(Pageable pageable);

    /**
     * 查找有堆栈跟踪的日志
     */
    Page<SystemLog> findByStackTraceIsNotNullOrderByLogTimeDesc(Pageable pageable);

    /**
     * 统计各日志级别数量
     */
    @Query("SELECT l.logLevel, COUNT(l) FROM SystemLog l GROUP BY l.logLevel")
    List<Object[]> countByLogLevel();

    /**
     * 统计各日志来源数量
     */
    @Query("SELECT l.logSource, COUNT(l) FROM SystemLog l GROUP BY l.logSource")
    List<Object[]> countByLogSource();

    /**
     * 统计指定时间范围内的日志数量
     */
    long countByLogTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

//    /**
//     * 按小时统计日志数量
//     */
//    @Query("SELECT DATE_FORMAT(l.logTime, '%Y-%m-%d %H:00:00') as hour, COUNT(l) as logCount " +
//           "FROM SystemLog l WHERE l.logTime >= :startTime " +
//           "GROUP BY DATE_FORMAT(l.logTime, '%Y-%m-%d %H:00:00') " +
//           "ORDER BY hour")
//    List<Object[]> countLogsByHour(@Param("startTime") LocalDateTime startTime);

    /**
     * 按天统计日志数量
     */
//    @Query("SELECT DATE(l.logTime) as day, COUNT(l) as logCount " +
//           "FROM SystemLog l WHERE l.logTime >= :startTime " +
//           "GROUP BY DATE(l.logTime) " +
//           "ORDER BY day")
//    List<Object[]> countLogsByDay(@Param("startTime") LocalDateTime startTime);

    /**
     * 统计错误日志数量
     */
    @Query("SELECT COUNT(l) FROM SystemLog l WHERE l.logLevel IN ('ERROR', 'FATAL') AND l.logTime >= :startTime")
    long countErrorLogs(@Param("startTime") LocalDateTime startTime);

    /**
     * 查找最频繁的错误
     */
    @Query("SELECT l.message, COUNT(l) as errorCount FROM SystemLog l " +
           "WHERE l.logLevel IN ('ERROR', 'FATAL') AND l.logTime >= :startTime " +
           "GROUP BY l.message ORDER BY errorCount DESC")
    List<Object[]> findMostFrequentErrors(@Param("startTime") LocalDateTime startTime, Pageable pageable);

    /**
     * 查找最活跃的Logger
     */
    @Query("SELECT l.loggerName, COUNT(l) as logCount FROM SystemLog l " +
           "WHERE l.logTime >= :startTime " +
           "GROUP BY l.loggerName ORDER BY logCount DESC")
    List<Object[]> findMostActiveLoggers(@Param("startTime") LocalDateTime startTime, Pageable pageable);

    /**
     * 查找最活跃的用户
     */
    @Query("SELECT l.userId, COUNT(l) as logCount FROM SystemLog l " +
           "WHERE l.userId IS NOT NULL AND l.logTime >= :startTime " +
           "GROUP BY l.userId ORDER BY logCount DESC")
    List<Object[]> findMostActiveUsers(@Param("startTime") LocalDateTime startTime, Pageable pageable);

    /**
     * 删除指定时间之前的日志
     */
    void deleteByLogTimeBefore(LocalDateTime cutoffTime);

    /**
     * 查找所有不同的Logger名称
     */
    @Query("SELECT DISTINCT l.loggerName FROM SystemLog l ORDER BY l.loggerName")
    List<String> findDistinctLoggerNames();

    /**
     * 查找所有不同的线程名称
     */
    @Query("SELECT DISTINCT l.threadName FROM SystemLog l WHERE l.threadName IS NOT NULL ORDER BY l.threadName")
    List<String> findDistinctThreadNames();

    /**
     * 查找所有不同的应用名称
     */
    @Query("SELECT DISTINCT l.applicationName FROM SystemLog l WHERE l.applicationName IS NOT NULL ORDER BY l.applicationName")
    List<String> findDistinctApplicationNames();

    /**
     * 查找所有不同的服务器名称
     */
    @Query("SELECT DISTINCT l.serverName FROM SystemLog l WHERE l.serverName IS NOT NULL ORDER BY l.serverName")
    List<String> findDistinctServerNames();

    long countByLogTimeBefore(LocalDateTime logTimeBefore);
}
