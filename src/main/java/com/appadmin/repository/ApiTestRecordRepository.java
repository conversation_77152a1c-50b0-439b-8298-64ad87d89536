package com.appadmin.repository;

import com.appadmin.entity.ApiTestRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * API测试记录仓储接口
 */
@Repository
public interface ApiTestRecordRepository extends JpaRepository<ApiTestRecord, Long> {

    /**
     * 根据API文档ID查找测试记录
     */
    Page<ApiTestRecord> findByApiDocumentIdOrderByTestTimeDesc(Long apiDocumentId, Pageable pageable);

    /**
     * 查找所有测试记录
     */
    Page<ApiTestRecord> findAllByOrderByTestTimeDesc(Pageable pageable);

    /**
     * 根据测试结果查找测试记录
     */
    Page<ApiTestRecord> findByTestResultOrderByTestTimeDesc(ApiTestRecord.TestResult testResult, Pageable pageable);

    /**
     * 根据测试类型查找测试记录
     */
    Page<ApiTestRecord> findByTestTypeOrderByTestTimeDesc(ApiTestRecord.TestType testType, Pageable pageable);

    /**
     * 根据测试人员查找测试记录
     */
    Page<ApiTestRecord> findByTestedByOrderByTestTimeDesc(String testedBy, Pageable pageable);

    /**
     * 根据测试环境查找测试记录
     */
    Page<ApiTestRecord> findByTestEnvironmentOrderByTestTimeDesc(String testEnvironment, Pageable pageable);

    /**
     * 根据时间范围查找测试记录
     */
    Page<ApiTestRecord> findByTestTimeBetweenOrderByTestTimeDesc(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 统计指定时间范围内的测试数量
     */
    long countByTestTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据测试结果统计数量
     */
    long countByTestResult(ApiTestRecord.TestResult testResult);

    /**
     * 统计各测试结果数量
     */
    @Query("SELECT t.testResult, COUNT(t) FROM ApiTestRecord t GROUP BY t.testResult")
    List<Object[]> countByTestResult();

    /**
     * 统计各测试类型数量
     */
    @Query("SELECT t.testType, COUNT(t) FROM ApiTestRecord t GROUP BY t.testType")
    List<Object[]> countByTestType();

    /**
     * 统计各测试环境数量
     */
    @Query("SELECT t.testEnvironment, COUNT(t) FROM ApiTestRecord t WHERE t.testEnvironment IS NOT NULL GROUP BY t.testEnvironment")
    List<Object[]> countByTestEnvironment();

    /**
     * 统计各测试人员的测试数量
     */
    @Query("SELECT t.testedBy, COUNT(t) FROM ApiTestRecord t WHERE t.testedBy IS NOT NULL GROUP BY t.testedBy ORDER BY COUNT(t) DESC")
    List<Object[]> countByTestedBy(Pageable pageable);

    /**
     * 获取平均响应时间
     */
    @Query("SELECT AVG(t.responseTime) FROM ApiTestRecord t WHERE t.responseTime IS NOT NULL")
    Double getAverageResponseTime();

    /**
     * 查找最慢的API
     */
    @Query("SELECT t.apiDocument.apiName, t.apiDocument.apiPath, AVG(t.responseTime) as avgResponseTime " +
           "FROM ApiTestRecord t WHERE t.responseTime IS NOT NULL " +
           "GROUP BY t.apiDocument.id, t.apiDocument.apiName, t.apiDocument.apiPath " +
           "ORDER BY avgResponseTime DESC")
    List<Object[]> findSlowestApis(Pageable pageable);

    /**
     * 查找最快的API
     */
    @Query("SELECT t.apiDocument.apiName, t.apiDocument.apiPath, AVG(t.responseTime) as avgResponseTime " +
           "FROM ApiTestRecord t WHERE t.responseTime IS NOT NULL " +
           "GROUP BY t.apiDocument.id, t.apiDocument.apiName, t.apiDocument.apiPath " +
           "ORDER BY avgResponseTime ASC")
    List<Object[]> findFastestApis(Pageable pageable);

    /**
     * 查找失败率最高的API
     */
    @Query("SELECT t.apiDocument.apiName, t.apiDocument.apiPath, " +
           "COUNT(CASE WHEN t.testResult = 'FAILED' THEN 1 END) * 100.0 / COUNT(t) as failureRate " +
           "FROM ApiTestRecord t " +
           "GROUP BY t.apiDocument.id, t.apiDocument.apiName, t.apiDocument.apiPath " +
           "HAVING COUNT(t) >= 5 " +
           "ORDER BY failureRate DESC")
    List<Object[]> findHighestFailureRateApis(Pageable pageable);

    /**
     * 查找测试最频繁的API
     */
    @Query("SELECT t.apiDocument.apiName, t.apiDocument.apiPath, COUNT(t) as testCount " +
           "FROM ApiTestRecord t " +
           "GROUP BY t.apiDocument.id, t.apiDocument.apiName, t.apiDocument.apiPath " +
           "ORDER BY testCount DESC")
    List<Object[]> findMostTestedApis(Pageable pageable);

//    /**
//     * 按天统计测试数量
//     */
//    @Query("SELECT DATE(t.testTime) as testDate, COUNT(t) as testCount " +
//           "FROM ApiTestRecord t WHERE t.testTime >= :startDate " +
//           "GROUP BY DATE(t.testTime) " +
//           "ORDER BY testDate")
//    List<Object[]> countTestsByDay(@Param("startDate") LocalDateTime startDate);
//
//    /**
//     * 按小时统计测试数量
//     */
//    @Query("SELECT DATE_FORMAT(t.testTime, '%Y-%m-%d %H:00:00') as testHour, COUNT(t) as testCount " +
//           "FROM ApiTestRecord t WHERE t.testTime >= :startDate " +
//           "GROUP BY DATE_FORMAT(t.testTime, '%Y-%m-%d %H:00:00') " +
//           "ORDER BY testHour")
//    List<Object[]> countTestsByHour(@Param("startDate") LocalDateTime startDate);

    /**
     * 查找长时间运行的测试
     */
    @Query("SELECT t FROM ApiTestRecord t WHERE t.responseTime > :threshold ORDER BY t.responseTime DESC")
    List<ApiTestRecord> findLongRunningTests(@Param("threshold") Long threshold, Pageable pageable);

    /**
     * 查找最近的失败测试
     */
    @Query("SELECT t FROM ApiTestRecord t WHERE t.testResult = 'FAILED' ORDER BY t.testTime DESC")
    List<ApiTestRecord> findRecentFailedTests(Pageable pageable);

    /**
     * 查找特定API的最新测试记录
     */
    @Query("SELECT t FROM ApiTestRecord t WHERE t.apiDocument.id = :apiDocumentId ORDER BY t.testTime DESC")
    List<ApiTestRecord> findLatestTestsByApiDocument(@Param("apiDocumentId") Long apiDocumentId, Pageable pageable);

    /**
     * 查找性能测试记录
     */
    @Query("SELECT t FROM ApiTestRecord t WHERE t.testType IN ('PERFORMANCE', 'LOAD', 'STRESS') ORDER BY t.testTime DESC")
    Page<ApiTestRecord> findPerformanceTests(Pageable pageable);

    /**
     * 查找自动化测试记录
     */
    Page<ApiTestRecord> findByIsAutomatedTrueOrderByTestTimeDesc(Pageable pageable);

    /**
     * 查找手动测试记录
     */
    Page<ApiTestRecord> findByIsAutomatedFalseOrderByTestTimeDesc(Pageable pageable);

    /**
     * 删除指定时间之前的测试记录
     */
    void deleteByTestTimeBefore(LocalDateTime cutoffTime);

    /**
     * 查找所有不同的测试环境
     */
    @Query("SELECT DISTINCT t.testEnvironment FROM ApiTestRecord t WHERE t.testEnvironment IS NOT NULL ORDER BY t.testEnvironment")
    List<String> findDistinctTestEnvironments();

    /**
     * 查找所有不同的测试人员
     */
    @Query("SELECT DISTINCT t.testedBy FROM ApiTestRecord t WHERE t.testedBy IS NOT NULL ORDER BY t.testedBy")
    List<String> findDistinctTesters();

    /**
     * 查找所有不同的测试套件
     */
    @Query("SELECT DISTINCT t.testSuite FROM ApiTestRecord t WHERE t.testSuite IS NOT NULL ORDER BY t.testSuite")
    List<String> findDistinctTestSuites();
}
