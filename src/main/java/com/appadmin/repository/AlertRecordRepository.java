package com.appadmin.repository;

import com.appadmin.entity.AlertRecord;
import com.appadmin.entity.AlertRule;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警记录仓储接口
 */
@Repository
public interface AlertRecordRepository extends JpaRepository<AlertRecord, Long> {

    /**
     * 根据告警规则和状态检查是否存在告警记录
     */
    boolean existsByAlertRuleAndStatusIn(AlertRule alertRule, List<AlertRecord.AlertStatus> statuses);

    /**
     * 根据状态查找告警记录
     */
    Page<AlertRecord> findByStatusOrderByTriggeredAtDesc(AlertRecord.AlertStatus status, Pageable pageable);

    /**
     * 根据状态列表查找告警记录
     */
    List<AlertRecord> findByStatusInOrderByTriggeredAtDesc(List<AlertRecord.AlertStatus> statuses);

    /**
     * 统计指定状态的告警数量
     */
    long countByStatusIn(List<AlertRecord.AlertStatus> statuses);

    /**
     * 根据严重程度查找告警记录
     */
    List<AlertRecord> findBySeverityOrderByTriggeredAtDesc(AlertRule.AlertSeverity severity);

    /**
     * 根据告警规则查找告警记录
     */
    Page<AlertRecord> findByAlertRuleOrderByTriggeredAtDesc(AlertRule alertRule, Pageable pageable);

    /**
     * 查找指定时间范围内的告警记录
     */
    List<AlertRecord> findByTriggeredAtBetweenOrderByTriggeredAtDesc(
        LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找未确认的告警记录
     */
    List<AlertRecord> findByStatusAndAcknowledgedAtIsNullOrderByTriggeredAtDesc(AlertRecord.AlertStatus status);

    /**
     * 查找未解决的告警记录
     */
    List<AlertRecord> findByStatusInAndResolvedAtIsNullOrderByTriggeredAtDesc(List<AlertRecord.AlertStatus> statuses);

    /**
     * 根据确认人查找告警记录
     */
    List<AlertRecord> findByAcknowledgedByOrderByAcknowledgedAtDesc(String acknowledgedBy);

    /**
     * 根据解决人查找告警记录
     */
    List<AlertRecord> findByResolvedByOrderByResolvedAtDesc(String resolvedBy);

    /**
     * 统计各状态的告警数量
     */
    @Query("SELECT a.status, COUNT(a) FROM AlertRecord a GROUP BY a.status")
    List<Object[]> countByStatus();

    /**
     * 统计各严重程度的告警数量
     */
    @Query("SELECT a.severity, COUNT(a) FROM AlertRecord a GROUP BY a.severity")
    List<Object[]> countBySeverity();

    /**
     * 统计指定时间范围内的告警数量
     */
    long countByTriggeredAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 按天统计告警数量
     */
//    @Query("SELECT DATE(a.triggeredAt), COUNT(a) FROM AlertRecord a WHERE a.triggeredAt >= :startDate " +
//           "GROUP BY DATE(a.triggeredAt) ORDER BY DATE(a.triggeredAt)")
//    List<Object[]> countDailyAlerts(@Param("startDate") LocalDateTime startDate);

//    /**
//     * 按小时统计告警数量
//     */
//    @Query("SELECT DATE_FORMAT(a.triggeredAt, '%Y-%m-%d %H:00:00'), COUNT(a) FROM AlertRecord a " +
//           "WHERE a.triggeredAt >= :startDate " +
//           "GROUP BY DATE_FORMAT(a.triggeredAt, '%Y-%m-%d %H:00:00') " +
//           "ORDER BY DATE_FORMAT(a.triggeredAt, '%Y-%m-%d %H:00:00')")
//    List<Object[]> countHourlyAlerts(@Param("startDate") LocalDateTime startDate);
//
//    /**
//     * 查找最频繁触发的告警规则
//     */
//    @Query("SELECT a.alertRule, COUNT(a) as alertCount FROM AlertRecord a " +
//           "WHERE a.triggeredAt >= :startDate " +
//           "GROUP BY a.alertRule ORDER BY alertCount DESC")
//    List<Object[]> findMostFrequentAlerts(@Param("startDate") LocalDateTime startDate, Pageable pageable);

//    /**
//     * 查找平均解决时间最长的告警规则
//     */
//    @Query("SELECT a.alertRule, AVG(TIMESTAMPDIFF(MINUTE, a.triggeredAt, a.resolvedAt)) as avgResolutionTime " +
//           "FROM AlertRecord a WHERE a.resolvedAt IS NOT NULL AND a.triggeredAt >= :startDate " +
//           "GROUP BY a.alertRule ORDER BY avgResolutionTime DESC")
//    List<Object[]> findSlowestResolutionAlerts(@Param("startDate") LocalDateTime startDate, Pageable pageable);

    /**
     * 查找长时间未解决的告警
     */
    @Query("SELECT a FROM AlertRecord a WHERE a.status IN :statuses AND " +
           "a.triggeredAt < :cutoffTime ORDER BY a.triggeredAt ASC")
    List<AlertRecord> findLongRunningAlerts(@Param("statuses") List<AlertRecord.AlertStatus> statuses,
                                           @Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找通知失败的告警
     */
    List<AlertRecord> findByNotificationStatusOrderByTriggeredAtDesc(AlertRecord.NotificationStatus notificationStatus);

    /**
     * 查找需要重试通知的告警
     */
    @Query("SELECT a FROM AlertRecord a WHERE a.notificationStatus = 'FAILED' AND " +
           "a.notificationAttempts < 3 AND a.lastNotificationAt < :retryTime")
    List<AlertRecord> findAlertsForNotificationRetry(@Param("retryTime") LocalDateTime retryTime);

    /**
     * 删除指定时间之前的告警记录
     */
    void deleteByTriggeredAtBefore(LocalDateTime cutoffTime);

    /**
     * 搜索告警记录
     */
    @Query("SELECT a FROM AlertRecord a WHERE " +
           "(LOWER(a.alertTitle) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.alertMessage) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<AlertRecord> searchAlerts(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 查找指定用户相关的告警记录
     */
    @Query("SELECT a FROM AlertRecord a WHERE a.acknowledgedBy = :username OR a.resolvedBy = :username " +
           "ORDER BY a.triggeredAt DESC")
    List<AlertRecord> findAlertsByUser(@Param("username") String username);
}
