package com.appadmin.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 项目环境实体
 */
@Entity
@Table(name = "project_environments", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"project_id", "environment_name"})
})
public class ProjectEnvironment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", nullable = false)
    private Project project;

    @Column(nullable = false, name = "environment_name")
    private String environmentName;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private EnvironmentType environmentType;

    @Column(columnDefinition = "TEXT")
    private String description;

    private String serverHost;

    private Integer serverPort;

    private String deployPath;

    private String applicationUrl;

    private String databaseUrl;

    private String databaseUsername;

    private String databasePassword;

    @Column(columnDefinition = "TEXT")
    private String environmentVariables;

    @Column(columnDefinition = "TEXT")
    private String configOverrides;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private EnvironmentStatus status = EnvironmentStatus.INACTIVE;

    @Column(nullable = false)
    private Boolean isActive = true;

    private Integer deployOrder = 0;

    @Column(nullable = false)
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(nullable = false)
    private LocalDateTime updatedAt = LocalDateTime.now();

    private String createdBy;

    private LocalDateTime lastDeployedAt;

    private String lastDeployedBy;

    private String deployVersion;

    @Column(columnDefinition = "TEXT")
    private String deployNotes;

    // 构造函数
    public ProjectEnvironment() {
    }

    public ProjectEnvironment(Project project, String environmentName, EnvironmentType environmentType) {
        this.project = project;
        this.environmentName = environmentName;
        this.environmentType = environmentType;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Project getProject() {
        return project;
    }

    public void setProject(Project project) {
        this.project = project;
    }

    public String getEnvironmentName() {
        return environmentName;
    }

    public void setEnvironmentName(String environmentName) {
        this.environmentName = environmentName;
    }

    public EnvironmentType getEnvironmentType() {
        return environmentType;
    }

    public void setEnvironmentType(EnvironmentType environmentType) {
        this.environmentType = environmentType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getServerHost() {
        return serverHost;
    }

    public void setServerHost(String serverHost) {
        this.serverHost = serverHost;
    }

    public Integer getServerPort() {
        return serverPort;
    }

    public void setServerPort(Integer serverPort) {
        this.serverPort = serverPort;
    }

    public String getDeployPath() {
        return deployPath;
    }

    public void setDeployPath(String deployPath) {
        this.deployPath = deployPath;
    }

    public String getApplicationUrl() {
        return applicationUrl;
    }

    public void setApplicationUrl(String applicationUrl) {
        this.applicationUrl = applicationUrl;
    }

    public String getDatabaseUrl() {
        return databaseUrl;
    }

    public void setDatabaseUrl(String databaseUrl) {
        this.databaseUrl = databaseUrl;
    }

    public String getDatabaseUsername() {
        return databaseUsername;
    }

    public void setDatabaseUsername(String databaseUsername) {
        this.databaseUsername = databaseUsername;
    }

    public String getDatabasePassword() {
        return databasePassword;
    }

    public void setDatabasePassword(String databasePassword) {
        this.databasePassword = databasePassword;
    }

    public String getEnvironmentVariables() {
        return environmentVariables;
    }

    public void setEnvironmentVariables(String environmentVariables) {
        this.environmentVariables = environmentVariables;
    }

    public String getConfigOverrides() {
        return configOverrides;
    }

    public void setConfigOverrides(String configOverrides) {
        this.configOverrides = configOverrides;
    }

    public EnvironmentStatus getStatus() {
        return status;
    }

    public void setStatus(EnvironmentStatus status) {
        this.status = status;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Integer getDeployOrder() {
        return deployOrder;
    }

    public void setDeployOrder(Integer deployOrder) {
        this.deployOrder = deployOrder;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getLastDeployedAt() {
        return lastDeployedAt;
    }

    public void setLastDeployedAt(LocalDateTime lastDeployedAt) {
        this.lastDeployedAt = lastDeployedAt;
    }

    public String getLastDeployedBy() {
        return lastDeployedBy;
    }

    public void setLastDeployedBy(String lastDeployedBy) {
        this.lastDeployedBy = lastDeployedBy;
    }

    public String getDeployVersion() {
        return deployVersion;
    }

    public void setDeployVersion(String deployVersion) {
        this.deployVersion = deployVersion;
    }

    public String getDeployNotes() {
        return deployNotes;
    }

    public void setDeployNotes(String deployNotes) {
        this.deployNotes = deployNotes;
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 环境类型枚举
     */
    public enum EnvironmentType {
        DEVELOPMENT("开发环境"),
        TESTING("测试环境"),
        STAGING("预发布环境"),
        PRODUCTION("生产环境"),
        DEMO("演示环境"),
        SANDBOX("沙箱环境");

        private final String description;

        EnvironmentType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 检查是否为生产环境
         */
        public boolean isProduction() {
            return this == PRODUCTION;
        }

        /**
         * 获取环境优先级（用于排序）
         */
        public int getPriority() {
            switch (this) {
                case DEVELOPMENT:
                    return 1;
                case TESTING:
                    return 2;
                case STAGING:
                    return 3;
                case PRODUCTION:
                    return 4;
                case DEMO:
                    return 5;
                case SANDBOX:
                    return 6;
                default:
                    return 99;
            }
        }
    }

    /**
     * 环境状态枚举
     */
    public enum EnvironmentStatus {
        ACTIVE("运行中"),
        INACTIVE("未运行"),
        DEPLOYING("部署中"),
        ERROR("错误"),
        MAINTENANCE("维护中");

        private final String description;

        EnvironmentStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return environmentName + " (" + environmentType.getDescription() + ")";
    }

    /**
     * 获取完整的服务器地址
     */
    public String getFullServerAddress() {
        if (serverHost == null) return null;
        if (serverPort == null) return serverHost;
        return serverHost + ":" + serverPort;
    }

    /**
     * 标记部署
     */
    public void markDeployed(String deployedBy, String version, String notes) {
        this.lastDeployedAt = LocalDateTime.now();
        this.lastDeployedBy = deployedBy;
        this.deployVersion = version;
        this.deployNotes = notes;
        this.status = EnvironmentStatus.ACTIVE;
    }

    /**
     * 检查是否可以部署
     */
    public boolean canDeploy() {
        return status != EnvironmentStatus.DEPLOYING && status != EnvironmentStatus.MAINTENANCE;
    }
}
