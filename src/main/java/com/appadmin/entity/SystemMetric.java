package com.appadmin.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 系统指标实体
 */
@Entity
@Table(name = "system_metrics")
public class SystemMetric {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String metricName;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MetricType metricType;

    @Column(nullable = false, name = "val")
    private Double value;

    private String unit;

    private String source;

    private String tags;

    @Column(nullable = false)
    private LocalDateTime timestamp = LocalDateTime.now();

    @Column(columnDefinition = "TEXT")
    private String metadata;

    // 构造函数
    public SystemMetric() {
    }

    public SystemMetric(String metricName, MetricType metricType, Double value, String unit) {
        this.metricName = metricName;
        this.metricType = metricType;
        this.value = value;
        this.unit = unit;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMetricName() {
        return metricName;
    }

    public void setMetricName(String metricName) {
        this.metricName = metricName;
    }

    public MetricType getMetricType() {
        return metricType;
    }

    public void setMetricType(MetricType metricType) {
        this.metricType = metricType;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    /**
     * 指标类型枚举
     */
    public enum MetricType {
        CPU_USAGE("CPU使用率"),
        MEMORY_USAGE("内存使用率"),
        DISK_USAGE("磁盘使用率"),
        NETWORK_IO("网络IO"),
        DATABASE_CONNECTIONS("数据库连接数"),
        APPLICATION_RESPONSE_TIME("应用响应时间"),
        ERROR_RATE("错误率"),
        THROUGHPUT("吞吐量"),
        CUSTOM("自定义指标");

        private final String description;

        MetricType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 获取格式化的值
     */
    public String getFormattedValue() {
        if (unit != null) {
            return String.format("%.2f %s", value, unit);
        }
        return String.format("%.2f", value);
    }

    /**
     * 检查是否为百分比指标
     */
    public boolean isPercentageMetric() {
        return metricType == MetricType.CPU_USAGE ||
                metricType == MetricType.MEMORY_USAGE ||
                metricType == MetricType.DISK_USAGE ||
                metricType == MetricType.ERROR_RATE;
    }

    /**
     * 获取指标级别
     */
    public MetricLevel getMetricLevel() {
        if (isPercentageMetric()) {
            if (value >= 90) return MetricLevel.CRITICAL;
            if (value >= 80) return MetricLevel.WARNING;
            if (value >= 70) return MetricLevel.INFO;
            return MetricLevel.NORMAL;
        }

        // 对于其他类型的指标，需要根据具体情况判断
        return MetricLevel.NORMAL;
    }

    /**
     * 指标级别枚举
     */
    public enum MetricLevel {
        NORMAL("正常"),
        INFO("信息"),
        WARNING("警告"),
        CRITICAL("严重");

        private final String description;

        MetricLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
