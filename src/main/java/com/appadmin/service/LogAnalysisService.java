package com.appadmin.service;

import com.appadmin.entity.SystemLog;
import com.appadmin.entity.OperationLog;
import com.appadmin.repository.SystemLogRepository;
import com.appadmin.repository.OperationLogRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 日志分析服务
 */
@Service
@Transactional
public class LogAnalysisService {
    private static final Logger logger = LoggerFactory.getLogger(LogAnalysisService.class);
    
    @Autowired
    private SystemLogRepository systemLogRepository;
    
    @Autowired
    private OperationLogRepository operationLogRepository;
    
    // 日志保留配置
    private static final int SYSTEM_LOG_RETENTION_DAYS = 30;
    private static final int OPERATION_LOG_RETENTION_DAYS = 90;

    // ========== 系统日志管理 ==========
    
    /**
     * 记录系统日志
     */
    @Async
    public void recordSystemLog(SystemLog.LogLevel logLevel, String loggerName, String message, 
                               Throwable throwable, String userId, String sessionId, String requestId) {
        try {
            SystemLog systemLog = new SystemLog(logLevel, loggerName, message);
            
            if (throwable != null) {
                systemLog.setStackTrace(getStackTrace(throwable));
            }
            
            // 设置上下文信息
            systemLog.setUserId(userId);
            systemLog.setSessionId(sessionId);
            systemLog.setRequestId(requestId);
            systemLog.setThreadName(Thread.currentThread().getName());
            
            // 设置调用位置信息
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            if (stackTrace.length > 3) {
                StackTraceElement element = stackTrace[3]; // 跳过当前方法和调用方法
                systemLog.setClassName(element.getClassName());
                systemLog.setMethodName(element.getMethodName());
                systemLog.setFileName(element.getFileName());
                systemLog.setLineNumber(element.getLineNumber());
            }
            
            // 设置服务器信息
            systemLog.setApplicationName("AppAdmin");
            systemLog.setServerName(getServerName());
            systemLog.setServerIp(getServerIp());
            
            systemLogRepository.save(systemLog);
            
        } catch (Exception e) {
            // 避免日志记录失败影响主业务
            logger.error("记录系统日志失败: {}", e.getMessage());
        }
    }

    /**
     * 查询系统日志
     */
    public Page<SystemLog> getSystemLogs(SystemLog.LogLevel logLevel, SystemLog.LogSource logSource,
                                        LocalDateTime startTime, LocalDateTime endTime, 
                                        String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return systemLogRepository.searchLogs(logLevel, logSource, startTime, endTime, keyword, pageable);
    }

    /**
     * 获取错误日志
     */
    public Page<SystemLog> getErrorLogs(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return systemLogRepository.findErrorLogs(pageable);
    }

    /**
     * 获取系统日志统计
     */
    public Map<String, Object> getSystemLogStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfDay = now.withHour(0).withMinute(0).withSecond(0);
        LocalDateTime startOfWeek = now.minusDays(7);
        
        // 基本统计
        stats.put("totalLogs", systemLogRepository.count());
        stats.put("todayLogs", systemLogRepository.countByLogTimeBetween(startOfDay, now));
        stats.put("weeklyErrorLogs", systemLogRepository.countErrorLogs(startOfWeek));
        
        // 按级别统计
        stats.put("logsByLevel", systemLogRepository.countByLogLevel());
        
        // 按来源统计
        stats.put("logsBySource", systemLogRepository.countByLogSource());
        
        // 最频繁的错误
        stats.put("frequentErrors", systemLogRepository.findMostFrequentErrors(startOfWeek, PageRequest.of(0, 10)));
        
        // 最活跃的Logger
        stats.put("activeLoggers", systemLogRepository.findMostActiveLoggers(startOfWeek, PageRequest.of(0, 10)));
        
        // 小时统计
//        stats.put("hourlyStats", systemLogRepository.countLogsByHour(startOfDay));
        
        return stats;
    }

    // ========== 操作日志管理 ==========
    
    /**
     * 记录操作日志
     */
    @Async
    public void recordOperationLog(String userId, String username, String module, 
                                  OperationLog.OperationType operationType, String operationName,
                                  String operationDesc, HttpServletRequest request, 
                                  Object requestParams, Object responseData, 
                                  long executionTime, Throwable throwable) {
        try {
            OperationLog operationLog = new OperationLog(userId, module, operationType, operationName);
            
            // 设置用户信息
            operationLog.setUsername(username);
            
            // 设置操作描述
            operationLog.setOperationDesc(operationDesc);
            
            // 设置请求信息
            if (request != null) {
                operationLog.setRequestMethod(request.getMethod());
                operationLog.setRequestUrl(request.getRequestURL().toString());
                operationLog.setClientIp(getClientIp(request));
                operationLog.setUserAgent(request.getHeader("User-Agent"));
                operationLog.setSessionId(request.getSession().getId());
                
                // 设置请求参数
                if (requestParams != null) {
                    operationLog.setRequestParams(objectToJson(requestParams));
                }
                
                // 设置请求体（POST/PUT请求）
                if ("POST".equals(request.getMethod()) || "PUT".equals(request.getMethod())) {
                    operationLog.setRequestBody(objectToJson(requestParams));
                }
            }
            
            // 设置响应数据
            if (responseData != null) {
                operationLog.setResponseData(objectToJson(responseData));
            }
            
            // 设置执行时间
            operationLog.setExecutionTime(executionTime);
            
            // 设置状态和错误信息
            if (throwable != null) {
                operationLog.setStatus(OperationLog.OperationStatus.FAILURE);
                operationLog.setErrorMessage(throwable.getMessage());
                operationLog.setStackTrace(getStackTrace(throwable));
            } else {
                operationLog.setStatus(OperationLog.OperationStatus.SUCCESS);
            }
            
            // 生成请求ID
            operationLog.setRequestId(generateRequestId());
            
            operationLogRepository.save(operationLog);
            
        } catch (Exception e) {
            // 避免日志记录失败影响主业务
            logger.error("记录操作日志失败: {}", e.getMessage());
        }
    }

    /**
     * 查询操作日志
     */
    public Page<OperationLog> getOperationLogs(String userId, String module, 
                                              OperationLog.OperationType operationType,
                                              OperationLog.OperationStatus status,
                                              LocalDateTime startTime, LocalDateTime endTime,
                                              String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return operationLogRepository.searchOperationLogs(userId, module, operationType, status, 
                                                         startTime, endTime, keyword, pageable);
    }

    /**
     * 获取操作日志统计
     */
    public Map<String, Object> getOperationLogStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfDay = now.withHour(0).withMinute(0).withSecond(0);
        LocalDateTime startOfWeek = now.minusDays(7);
        
        // 基本统计
        stats.put("totalOperations", operationLogRepository.count());
        stats.put("todayOperations", operationLogRepository.countByOperationTimeBetween(startOfDay, now));
        stats.put("weeklyFailedOperations", operationLogRepository.countFailedOperations(startOfWeek));
        
        // 按类型统计
        stats.put("operationsByType", operationLogRepository.countByOperationType());
        
        // 按模块统计
        stats.put("operationsByModule", operationLogRepository.countByModule());
        
        // 按状态统计
        stats.put("operationsByStatus", operationLogRepository.countByStatus());
        
        // 最频繁的操作
        stats.put("frequentOperations", operationLogRepository.findMostFrequentOperations(startOfWeek, PageRequest.of(0, 10)));
        
        // 最慢的操作
        stats.put("slowestOperations", operationLogRepository.findSlowestOperations(startOfWeek, PageRequest.of(0, 10)));
        
        // 最活跃的用户
        stats.put("activeUsers", operationLogRepository.countOperationsByUser(startOfWeek, PageRequest.of(0, 10)));
        
        // 小时统计
//        stats.put("hourlyStats", operationLogRepository.countOperationsByHour(startOfDay));
        
        return stats;
    }

    // ========== 日志清理 ==========
    
    /**
     * 定时清理过期日志
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanupExpiredLogs() {
        try {
            LocalDateTime systemLogCutoff = LocalDateTime.now().minusDays(SYSTEM_LOG_RETENTION_DAYS);
            LocalDateTime operationLogCutoff = LocalDateTime.now().minusDays(OPERATION_LOG_RETENTION_DAYS);

            // 清理系统日志
            long systemLogCount = systemLogRepository.countByLogTimeBefore(systemLogCutoff);
            if (systemLogCount > 0) {
                systemLogRepository.deleteByLogTimeBefore(systemLogCutoff);
                logger.info("清理过期系统日志: {} 条", systemLogCount);
            }
            
            // 清理操作日志
            long operationLogCount = operationLogRepository.countByOperationTimeBefore(operationLogCutoff);
            if (operationLogCount > 0) {
                operationLogRepository.deleteByOperationTimeBefore(operationLogCutoff);
                logger.info("清理过期操作日志: {} 条", operationLogCount);
            }
            
        } catch (Exception e) {
            logger.error("清理过期日志失败: {}", e.getMessage(), e);
        }
    }

    // ========== 私有方法 ==========
    
    /**
     * 获取堆栈跟踪字符串
     */
    private String getStackTrace(Throwable throwable) {
        if (throwable == null) return null;
        
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 对象转JSON字符串
     */
    private String objectToJson(Object obj) {
        if (obj == null) return null;
        
        try {
            // 这里可以使用Jackson或其他JSON库
            return obj.toString();
        } catch (Exception e) {
            return "转换失败: " + e.getMessage();
        }
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 获取服务器名称
     */
    private String getServerName() {
        try {
            return java.net.InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * 获取服务器IP
     */
    private String getServerIp() {
        try {
            return java.net.InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            return "unknown";
        }
    }
}
