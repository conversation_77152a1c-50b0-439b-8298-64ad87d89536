package com.appadmin.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class HomeController {
    @GetMapping("/")
    public String index() {
        return "index";
    }

    @GetMapping("/code-generator")
    public String codeGenerator(Model model) {
        return "code-generator";
    }

    @GetMapping("/remote-servers")
    public String remoteServers(Model model) {
        return "remote-servers";
    }

    @GetMapping("/ai-assistant")
    public String aiAssistant(Model model) {
        return "ai-assistant";
    }

    @GetMapping("/database-management")
    public String databaseManagement(Model model) {
        return "database-management";
    }

    @GetMapping("/project-management")
    public String projectManagement(Model model) {
        return "project-management";
    }

    @GetMapping("/config-management")
    public String configManagement(Model model) {
        return "config-management";
    }

    @GetMapping("/user-management")
    public String userManagement(Model model) {
        return "user-management";
    }

    @GetMapping("/monitoring")
    public String monitoring(Model model) {
        return "monitoring";
    }

    @GetMapping("/log-management")
    public String logManagement(Model model) {
        return "log-management";
    }

    @GetMapping("/api-management")
    public String apiManagement(Model model) {
        return "api-management";
    }

    @GetMapping("/test-page")
    public String testPage(Model model) {
        return "test-page";
    }
} 