2025-06-09 20:03:57 [restartedMain] INFO  com.appadmin.AppAdminApplication - Starting AppAdminApplication using Java 17.0.15 on DESKTOP-F5TR6NT with PID 16832 (C:\Users\<USER>\Desktop\app-admin\target\classes started by majack in C:\Users\<USER>\Desktop\app-admin)
2025-06-09 20:03:57 [restartedMain] DEBUG com.appadmin.AppAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-09 20:03:57 [restartedMain] INFO  com.appadmin.AppAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-09 20:03:57 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-09 20:03:57 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-09 20:03:58 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 20:03:59 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 233 ms. Found 26 JPA repository interfaces.
2025-06-09 20:04:00 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-09 20:04:00 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 20:04:00 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 20:04:00 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 20:04:00 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3078 ms
2025-06-09 20:04:00 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-09 20:04:01 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-09 20:04:01 [restartedMain] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:file:./appadmin'
2025-06-09 20:04:01 [restartedMain] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 20:04:01 [restartedMain] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 20:04:02 [restartedMain] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 20:04:02 [restartedMain] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-06-09 20:04:03 [restartedMain] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 20:04:03 [restartedMain] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 20:04:03 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 20:04:05 [restartedMain] INFO  com.appadmin.service.TemplateService - Initializing default templates...
2025-06-09 20:04:05 [restartedMain] INFO  com.appadmin.service.TemplateService - Templates already exist, skipping default template creation
2025-06-09 20:04:05 [restartedMain] INFO  c.a.s.SystemEnvironmentScanService - Operating System detected: windows 11
2025-06-09 20:04:05 [restartedMain] INFO  c.a.service.LogManagementService - Log directory initialized: C:\Users\<USER>\Desktop\app-admin\logs
2025-06-09 20:04:06 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a98e4915-f1c5-42dc-8cab-6ed6204f84a7

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-09 20:04:06 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3b8db6a4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6ba908a8, org.springframework.security.web.context.SecurityContextPersistenceFilter@73ed8912, org.springframework.security.web.header.HeaderWriterFilter@1906bb1, org.springframework.security.web.csrf.CsrfFilter@44e3e2d2, org.springframework.security.web.authentication.logout.LogoutFilter@5b216b9c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@23756899, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@764855be, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@66c2f8c1, org.springframework.security.web.session.SessionManagementFilter@170258e2, org.springframework.security.web.access.ExceptionTranslationFilter@b5e3530, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2975daef]
2025-06-09 20:04:06 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-09 20:04:07 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-09 20:04:07 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-09 20:04:07 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:04:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:04:07 [restartedMain] INFO  com.appadmin.AppAdminApplication - Started AppAdminApplication in 11.087 seconds (JVM running for 12.541)
2025-06-09 20:04:13 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 20:04:13 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 20:04:13 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-09 20:05:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:06:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:07:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:08:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:09:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:09:07 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:10:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:11:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:12:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:13:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:14:07 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:14:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:15:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:16:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:17:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:18:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:19:07 [scheduling-3] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:19:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:20:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:21:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:22:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:23:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:24:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:24:07 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:25:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:26:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:27:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:28:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:29:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:29:07 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:30:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:31:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:32:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:33:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:34:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:34:07 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:35:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:36:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:37:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:38:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:39:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:39:07 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:40:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:41:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:42:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:43:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:44:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:44:07 [scheduling-3] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:44:12 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 20:44:12 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-09 20:44:12 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-09 20:44:19 [restartedMain] INFO  com.appadmin.AppAdminApplication - Starting AppAdminApplication using Java 17.0.15 on DESKTOP-F5TR6NT with PID 14220 (C:\Users\<USER>\Desktop\app-admin\target\classes started by majack in C:\Users\<USER>\Desktop\app-admin)
2025-06-09 20:44:19 [restartedMain] DEBUG com.appadmin.AppAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-09 20:44:19 [restartedMain] INFO  com.appadmin.AppAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-09 20:44:19 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-09 20:44:19 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-09 20:44:20 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 20:44:20 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 166 ms. Found 26 JPA repository interfaces.
2025-06-09 20:44:21 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-09 20:44:21 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 20:44:21 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 20:44:21 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 20:44:21 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2187 ms
2025-06-09 20:44:21 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-09 20:44:22 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-09 20:44:22 [restartedMain] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:file:./appadmin'
2025-06-09 20:44:22 [restartedMain] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 20:44:22 [restartedMain] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 20:44:22 [restartedMain] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 20:44:23 [restartedMain] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-06-09 20:44:24 [restartedMain] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 20:44:24 [restartedMain] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 20:44:24 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 20:44:26 [restartedMain] INFO  com.appadmin.service.TemplateService - Initializing default templates...
2025-06-09 20:44:26 [restartedMain] INFO  com.appadmin.service.TemplateService - Templates already exist, skipping default template creation
2025-06-09 20:44:26 [restartedMain] INFO  c.a.s.SystemEnvironmentScanService - Operating System detected: windows 11
2025-06-09 20:44:26 [restartedMain] INFO  c.a.service.LogManagementService - Log directory initialized: C:\Users\<USER>\Desktop\app-admin\logs
2025-06-09 20:44:27 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 218e7058-3511-41a6-be1f-fd79e91d6299

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-09 20:44:28 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6c575a60, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2693f290, org.springframework.security.web.context.SecurityContextPersistenceFilter@6fb0809b, org.springframework.security.web.header.HeaderWriterFilter@3cf810c9, org.springframework.security.web.csrf.CsrfFilter@7242ef03, org.springframework.security.web.authentication.logout.LogoutFilter@61a4e4bc, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@422a6fd6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5648e51b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c14b02d, org.springframework.security.web.session.SessionManagementFilter@65c8b877, org.springframework.security.web.access.ExceptionTranslationFilter@51c1cfa0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2d16aeb5]
2025-06-09 20:44:28 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-09 20:44:29 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-09 20:44:29 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-09 20:44:29 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:44:29 [restartedMain] INFO  com.appadmin.AppAdminApplication - Started AppAdminApplication in 10.148 seconds (JVM running for 16.136)
2025-06-09 20:44:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:45:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:46:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:47:10 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 20:47:10 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 20:47:10 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-09 20:47:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:48:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:49:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:49:29 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:50:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:51:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:52:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:53:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:54:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:54:29 [scheduling-1] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:55:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:56:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:57:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:58:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:59:29 [scheduling-2] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:59:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:00:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:01:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:02:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:03:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:04:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:04:29 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:05:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:06:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:07:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:08:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:09:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:09:29 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:10:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:11:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:12:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:13:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:14:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:14:29 [scheduling-1] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:15:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:16:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:17:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:18:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:19:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:19:29 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:20:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:21:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:22:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:23:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:24:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:24:29 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:25:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:26:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:27:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:28:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:29:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:29:29 [scheduling-3] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:30:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:31:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:32:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:33:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:34:29 [scheduling-1] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:34:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:35:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:36:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:37:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:38:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:39:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:39:29 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:40:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:41:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:42:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:43:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:44:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:44:29 [scheduling-1] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:45:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:46:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:47:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:48:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:49:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:49:29 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:50:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:51:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:52:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:53:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:54:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:54:29 [scheduling-3] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:55:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:56:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:57:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:58:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:59:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:59:29 [scheduling-3] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:00:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:01:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:02:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:03:00 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 5 class path changes (1 addition, 1 deletion, 3 modifications)
2025-06-09 22:03:00 [Thread-6] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-09 22:03:00 [Thread-6] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-09 22:03:00 [Thread-6] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 22:03:00 [Thread-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-09 22:03:00 [Thread-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-09 22:03:00 [restartedMain] INFO  com.appadmin.AppAdminApplication - Starting AppAdminApplication using Java 17.0.15 on DESKTOP-F5TR6NT with PID 14220 (C:\Users\<USER>\Desktop\app-admin\target\classes started by majack in C:\Users\<USER>\Desktop\app-admin)
2025-06-09 22:03:00 [restartedMain] DEBUG com.appadmin.AppAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-09 22:03:00 [restartedMain] INFO  com.appadmin.AppAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-09 22:03:01 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 22:03:01 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 106 ms. Found 26 JPA repository interfaces.
2025-06-09 22:03:01 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-09 22:03:01 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 22:03:01 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 22:03:01 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 22:03:01 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 630 ms
2025-06-09 22:03:01 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-06-09 22:03:01 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-06-09 22:03:01 [restartedMain] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:file:./appadmin'
2025-06-09 22:03:01 [restartedMain] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 22:03:01 [restartedMain] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-06-09 22:03:01 [restartedMain] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 22:03:01 [restartedMain] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 22:03:01 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 22:03:02 [restartedMain] INFO  com.appadmin.service.TemplateService - Initializing default templates...
2025-06-09 22:03:02 [restartedMain] INFO  com.appadmin.service.TemplateService - Templates already exist, skipping default template creation
2025-06-09 22:03:02 [restartedMain] INFO  c.a.s.SystemEnvironmentScanService - Operating System detected: windows 11
2025-06-09 22:03:02 [restartedMain] INFO  c.a.service.LogManagementService - Log directory initialized: C:\Users\<USER>\Desktop\app-admin\logs
2025-06-09 22:03:03 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a6fdc4b0-e5b8-4d00-8fb2-eaf0e36e66e3

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-09 22:03:03 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1460414f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@54c8e598, org.springframework.security.web.context.SecurityContextPersistenceFilter@2dad7097, org.springframework.security.web.header.HeaderWriterFilter@2de0aa09, org.springframework.security.web.csrf.CsrfFilter@39d4f65a, org.springframework.security.web.authentication.logout.LogoutFilter@162fc335, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@62a3b025, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4abbee9a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1c5eea4f, org.springframework.security.web.session.SessionManagementFilter@33553681, org.springframework.security.web.access.ExceptionTranslationFilter@43a18c13, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1d5bb027]
2025-06-09 22:03:03 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-09 22:03:03 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-09 22:03:03 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-09 22:03:03 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:03:03 [restartedMain] INFO  com.appadmin.AppAdminApplication - Started AppAdminApplication in 2.725 seconds (JVM running for 4730.299)
2025-06-09 22:03:03 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-06-09 22:03:03 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:03:05 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-06-09 22:03:05 [Thread-8] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-09 22:03:05 [Thread-8] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 22:03:05 [Thread-8] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown initiated...
2025-06-09 22:03:06 [Thread-8] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Shutdown completed.
2025-06-09 22:03:06 [restartedMain] INFO  com.appadmin.AppAdminApplication - Starting AppAdminApplication using Java 17.0.15 on DESKTOP-F5TR6NT with PID 14220 (C:\Users\<USER>\Desktop\app-admin\target\classes started by majack in C:\Users\<USER>\Desktop\app-admin)
2025-06-09 22:03:06 [restartedMain] DEBUG com.appadmin.AppAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-09 22:03:06 [restartedMain] INFO  com.appadmin.AppAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-09 22:03:06 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 22:03:06 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 73 ms. Found 26 JPA repository interfaces.
2025-06-09 22:03:06 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-09 22:03:06 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 22:03:06 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 22:03:06 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 22:03:06 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 379 ms
2025-06-09 22:03:06 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-06-09 22:03:06 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Start completed.
2025-06-09 22:03:06 [restartedMain] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:file:./appadmin'
2025-06-09 22:03:06 [restartedMain] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 22:03:06 [restartedMain] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-06-09 22:03:06 [restartedMain] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 22:03:06 [restartedMain] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 22:03:06 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 22:03:07 [restartedMain] INFO  com.appadmin.service.TemplateService - Initializing default templates...
2025-06-09 22:03:07 [restartedMain] INFO  com.appadmin.service.TemplateService - Templates already exist, skipping default template creation
2025-06-09 22:03:07 [restartedMain] INFO  c.a.s.SystemEnvironmentScanService - Operating System detected: windows 11
2025-06-09 22:03:07 [restartedMain] INFO  c.a.service.LogManagementService - Log directory initialized: C:\Users\<USER>\Desktop\app-admin\logs
2025-06-09 22:03:07 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 47f17f61-73c6-4d3d-bb99-4c9516a77ccc

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-09 22:03:07 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@70744da5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2ee710bc, org.springframework.security.web.context.SecurityContextPersistenceFilter@79201f04, org.springframework.security.web.header.HeaderWriterFilter@5b8f29a7, org.springframework.security.web.csrf.CsrfFilter@2dd60798, org.springframework.security.web.authentication.logout.LogoutFilter@1436c93c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@128b7338, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@30eba2df, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@50332ec9, org.springframework.security.web.session.SessionManagementFilter@64572ed1, org.springframework.security.web.access.ExceptionTranslationFilter@1d7bbb, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@467e0ba9]
2025-06-09 22:03:07 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-09 22:03:08 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-09 22:03:08 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-09 22:03:08 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:03:08 [restartedMain] INFO  com.appadmin.AppAdminApplication - Started AppAdminApplication in 1.977 seconds (JVM running for 4734.99)
2025-06-09 22:03:08 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-06-09 22:03:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:03:30 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 22:03:30 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 22:03:30 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-09 22:04:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:05:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:06:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:07:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:08:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:08:08 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:09:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:10:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:11:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:12:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:13:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:13:08 [scheduling-1] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:14:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:15:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:16:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:17:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:18:08 [scheduling-1] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:18:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:19:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:20:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:21:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:22:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:23:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:23:08 [scheduling-2] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:24:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:25:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:26:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:27:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:28:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:28:08 [scheduling-2] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:29:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:30:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:31:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:32:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:33:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:33:08 [scheduling-3] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:34:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:35:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:36:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:37:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:38:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:38:08 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:39:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:40:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:41:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:42:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:43:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:43:08 [scheduling-3] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:44:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:45:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:46:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:47:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:48:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:48:08 [scheduling-2] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:49:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:50:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:51:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:52:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:53:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:53:08 [scheduling-3] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:54:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:55:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:56:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:57:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:58:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 22:58:08 [scheduling-3] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 22:59:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:00:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:01:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:02:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:03:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:03:08 [scheduling-1] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 23:04:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:05:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:06:00 [http-nio-8080-exec-5] ERROR org.thymeleaf.TemplateEngine - [THYMELEAF][http-nio-8080-exec-5] Exception processing template "user-management": Error resolving template [user-management], template might not exist or might not be accessible by any of the configured Template Resolvers
org.thymeleaf.exceptions.TemplateInputException: Error resolving template [user-management], template might not exist or might not be accessible by any of the configured Template Resolvers
	at org.thymeleaf.engine.TemplateManager.resolveTemplate(TemplateManager.java:869) ~[thymeleaf-3.0.15.RELEASE.jar:3.0.15.RELEASE]
	at org.thymeleaf.engine.TemplateManager.parseAndProcess(TemplateManager.java:607) ~[thymeleaf-3.0.15.RELEASE.jar:3.0.15.RELEASE]
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1098) ~[thymeleaf-3.0.15.RELEASE.jar:3.0.15.RELEASE]
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1072) ~[thymeleaf-3.0.15.RELEASE.jar:3.0.15.RELEASE]
	at org.thymeleaf.spring5.view.ThymeleafView.renderFragment(ThymeleafView.java:366) ~[thymeleaf-spring5-3.0.15.RELEASE.jar:3.0.15.RELEASE]
	at org.thymeleaf.spring5.view.ThymeleafView.render(ThymeleafView.java:190) ~[thymeleaf-spring5-3.0.15.RELEASE.jar:3.0.15.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1405) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1149) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.base/java.lang.Thread.run(Thread.java:840) ~[na:na]
2025-06-09 23:06:00 [http-nio-8080-exec-5] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is org.thymeleaf.exceptions.TemplateInputException: Error resolving template [user-management], template might not exist or might not be accessible by any of the configured Template Resolvers] with root cause
org.thymeleaf.exceptions.TemplateInputException: Error resolving template [user-management], template might not exist or might not be accessible by any of the configured Template Resolvers
	at org.thymeleaf.engine.TemplateManager.resolveTemplate(TemplateManager.java:869) ~[thymeleaf-3.0.15.RELEASE.jar:3.0.15.RELEASE]
	at org.thymeleaf.engine.TemplateManager.parseAndProcess(TemplateManager.java:607) ~[thymeleaf-3.0.15.RELEASE.jar:3.0.15.RELEASE]
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1098) ~[thymeleaf-3.0.15.RELEASE.jar:3.0.15.RELEASE]
	at org.thymeleaf.TemplateEngine.process(TemplateEngine.java:1072) ~[thymeleaf-3.0.15.RELEASE.jar:3.0.15.RELEASE]
	at org.thymeleaf.spring5.view.ThymeleafView.renderFragment(ThymeleafView.java:366) ~[thymeleaf-spring5-3.0.15.RELEASE.jar:3.0.15.RELEASE]
	at org.thymeleaf.spring5.view.ThymeleafView.render(ThymeleafView.java:190) ~[thymeleaf-spring5-3.0.15.RELEASE.jar:3.0.15.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.render(DispatcherServlet.java:1405) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1149) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.23.jar:5.3.23]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779) ~[tomcat-embed-core-9.0.68.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) ~[tomcat-embed-websocket-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.csrf.CsrfFilter.doFilterInternal(CsrfFilter.java:117) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.4.jar:5.7.4]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.23.jar:5.3.23]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.23.jar:5.3.23]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.68.jar:9.0.68]
	at java.base/java.lang.Thread.run(Thread.java:840) ~[na:na]
2025-06-09 23:06:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:07:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:08:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:08:08 [scheduling-2] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 23:09:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:10:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:11:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:12:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:13:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:13:08 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 23:14:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:15:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:16:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:17:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:18:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:18:08 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 23:19:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:20:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:21:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:22:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:23:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:23:08 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 23:24:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:25:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:26:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:27:08 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:28:08 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:28:08 [scheduling-2] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 23:29:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:30:08 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:31:08 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 23:32:08 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
