2025-06-09 20:03:57 [restartedMain] INFO  com.appadmin.AppAdminApplication - Starting AppAdminApplication using Java 17.0.15 on DESKTOP-F5TR6NT with PID 16832 (C:\Users\<USER>\Desktop\app-admin\target\classes started by majack in C:\Users\<USER>\Desktop\app-admin)
2025-06-09 20:03:57 [restartedMain] DEBUG com.appadmin.AppAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-09 20:03:57 [restartedMain] INFO  com.appadmin.AppAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-09 20:03:57 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-09 20:03:57 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-09 20:03:58 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 20:03:59 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 233 ms. Found 26 JPA repository interfaces.
2025-06-09 20:04:00 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-09 20:04:00 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 20:04:00 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 20:04:00 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 20:04:00 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3078 ms
2025-06-09 20:04:00 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-09 20:04:01 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-09 20:04:01 [restartedMain] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:file:./appadmin'
2025-06-09 20:04:01 [restartedMain] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 20:04:01 [restartedMain] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 20:04:02 [restartedMain] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 20:04:02 [restartedMain] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-06-09 20:04:03 [restartedMain] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 20:04:03 [restartedMain] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 20:04:03 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 20:04:05 [restartedMain] INFO  com.appadmin.service.TemplateService - Initializing default templates...
2025-06-09 20:04:05 [restartedMain] INFO  com.appadmin.service.TemplateService - Templates already exist, skipping default template creation
2025-06-09 20:04:05 [restartedMain] INFO  c.a.s.SystemEnvironmentScanService - Operating System detected: windows 11
2025-06-09 20:04:05 [restartedMain] INFO  c.a.service.LogManagementService - Log directory initialized: C:\Users\<USER>\Desktop\app-admin\logs
2025-06-09 20:04:06 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a98e4915-f1c5-42dc-8cab-6ed6204f84a7

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-09 20:04:06 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3b8db6a4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6ba908a8, org.springframework.security.web.context.SecurityContextPersistenceFilter@73ed8912, org.springframework.security.web.header.HeaderWriterFilter@1906bb1, org.springframework.security.web.csrf.CsrfFilter@44e3e2d2, org.springframework.security.web.authentication.logout.LogoutFilter@5b216b9c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@23756899, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@764855be, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@66c2f8c1, org.springframework.security.web.session.SessionManagementFilter@170258e2, org.springframework.security.web.access.ExceptionTranslationFilter@b5e3530, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2975daef]
2025-06-09 20:04:06 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-09 20:04:07 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-09 20:04:07 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-09 20:04:07 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:04:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:04:07 [restartedMain] INFO  com.appadmin.AppAdminApplication - Started AppAdminApplication in 11.087 seconds (JVM running for 12.541)
2025-06-09 20:04:13 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 20:04:13 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 20:04:13 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-09 20:05:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:06:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:07:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:08:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:09:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:09:07 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:10:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:11:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:12:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:13:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:14:07 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:14:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:15:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:16:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:17:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:18:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:19:07 [scheduling-3] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:19:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:20:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:21:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:22:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:23:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:24:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:24:07 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:25:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:26:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:27:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:28:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:29:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:29:07 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:30:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:31:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:32:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:33:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:34:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:34:07 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:35:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:36:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:37:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:38:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:39:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:39:07 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:40:07 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:41:07 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:42:07 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:43:07 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:44:07 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:44:07 [scheduling-3] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:44:12 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 20:44:12 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-09 20:44:12 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-09 20:44:19 [restartedMain] INFO  com.appadmin.AppAdminApplication - Starting AppAdminApplication using Java 17.0.15 on DESKTOP-F5TR6NT with PID 14220 (C:\Users\<USER>\Desktop\app-admin\target\classes started by majack in C:\Users\<USER>\Desktop\app-admin)
2025-06-09 20:44:19 [restartedMain] DEBUG com.appadmin.AppAdminApplication - Running with Spring Boot v2.7.5, Spring v5.3.23
2025-06-09 20:44:19 [restartedMain] INFO  com.appadmin.AppAdminApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-09 20:44:19 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-09 20:44:19 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-09 20:44:20 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 20:44:20 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 166 ms. Found 26 JPA repository interfaces.
2025-06-09 20:44:21 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-09 20:44:21 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 20:44:21 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 20:44:21 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 20:44:21 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2187 ms
2025-06-09 20:44:21 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-09 20:44:22 [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-09 20:44:22 [restartedMain] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:file:./appadmin'
2025-06-09 20:44:22 [restartedMain] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 20:44:22 [restartedMain] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 20:44:22 [restartedMain] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 20:44:23 [restartedMain] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.H2Dialect
2025-06-09 20:44:24 [restartedMain] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 20:44:24 [restartedMain] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 20:44:24 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 20:44:26 [restartedMain] INFO  com.appadmin.service.TemplateService - Initializing default templates...
2025-06-09 20:44:26 [restartedMain] INFO  com.appadmin.service.TemplateService - Templates already exist, skipping default template creation
2025-06-09 20:44:26 [restartedMain] INFO  c.a.s.SystemEnvironmentScanService - Operating System detected: windows 11
2025-06-09 20:44:26 [restartedMain] INFO  c.a.service.LogManagementService - Log directory initialized: C:\Users\<USER>\Desktop\app-admin\logs
2025-06-09 20:44:27 [restartedMain] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 218e7058-3511-41a6-be1f-fd79e91d6299

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-09 20:44:28 [restartedMain] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6c575a60, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2693f290, org.springframework.security.web.context.SecurityContextPersistenceFilter@6fb0809b, org.springframework.security.web.header.HeaderWriterFilter@3cf810c9, org.springframework.security.web.csrf.CsrfFilter@7242ef03, org.springframework.security.web.authentication.logout.LogoutFilter@61a4e4bc, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@422a6fd6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5648e51b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c14b02d, org.springframework.security.web.session.SessionManagementFilter@65c8b877, org.springframework.security.web.access.ExceptionTranslationFilter@51c1cfa0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2d16aeb5]
2025-06-09 20:44:28 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-09 20:44:29 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-09 20:44:29 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-09 20:44:29 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:44:29 [restartedMain] INFO  com.appadmin.AppAdminApplication - Started AppAdminApplication in 10.148 seconds (JVM running for 16.136)
2025-06-09 20:44:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:45:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:46:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:47:10 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 20:47:10 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 20:47:10 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-09 20:47:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:48:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:49:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:49:29 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:50:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:51:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:52:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:53:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:54:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:54:29 [scheduling-1] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:55:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:56:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:57:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:58:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 20:59:29 [scheduling-2] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 20:59:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:00:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:01:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:02:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:03:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:04:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:04:29 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:05:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:06:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:07:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:08:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:09:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:09:29 [scheduling-5] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:10:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:11:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:12:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:13:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:14:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:14:29 [scheduling-1] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:15:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:16:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:17:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:18:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:19:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:19:29 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:20:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:21:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:22:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:23:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:24:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:24:29 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:25:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:26:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:27:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:28:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:29:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:29:29 [scheduling-3] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:30:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:31:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:32:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:33:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:34:29 [scheduling-1] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:34:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:35:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:36:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:37:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:38:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:39:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:39:29 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:40:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:41:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:42:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:43:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:44:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:44:29 [scheduling-1] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:45:29 [scheduling-2] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:46:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:47:29 [scheduling-4] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:48:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:49:29 [scheduling-5] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:49:29 [scheduling-4] DEBUG c.a.s.PerformanceMonitorService - Starting performance metrics collection
2025-06-09 21:50:29 [scheduling-3] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
2025-06-09 21:51:29 [scheduling-1] DEBUG c.a.service.HealthCheckService - Starting scheduled health checks
